# Database Configuration
DATABASE_TYPE=mysql
#SQLITE_DATABASE=default_db
MYSQL_HOST=gemini-balance-mysql
#MYSQL_SOCKET=/run/mysqld/mysqld.sock
MYSQL_PORT=3306
MYSQL_USER=gemini
MYSQL_PASSWORD=change_me
MYSQL_DATABASE=default_db
API_KEYS=["AIzaSyDsgR6vGIUGBUtk9sjl435A6l4gY9Ly5nQ","AIzaSyAaIeZTeZRhKdsLkXs9kvcMzxWnOHdMgkI","AIzaSyD73L16R5o8z5uMMdtG_uWUb2uJCI_QVFs","AIzaSyB6EkmeUsCa9U27ilvieK1VzhgTPTvAmMk"]
ALLOWED_TOKENS=["sk-123456"]
AUTH_TOKEN=sk-123456
# For Vertex AI Platform API Keys
VERTEX_API_KEYS=["AQ.Abxxxxxxxxxxxxxxxxxxx"]
# For Vertex AI Platform Express API Base URL
VERTEX_EXPRESS_BASE_URL=https://aiplatform.googleapis.com/v1beta1/publishers/google
TEST_MODEL=gemini-2.5-pro-preview-05-06
THINKING_MODELS=["gemini-2.5-flash-preview-05-20"]
THINKING_BUDGET_MAP={"gemini-2.5-flash-preview-05-20": 8000}
IMAGE_MODELS=["gemini-2.0-flash-exp"]
SEARCH_MODELS=["gemini-2.0-flash-exp","gemini-2.0-pro-exp"]
FILTERED_MODELS=["gemini-1.0-pro-vision-latest", "gemini-pro-vision", "chat-bison-001", "text-bison-001", "embedding-gecko-001"]
TOOLS_CODE_EXECUTION_ENABLED=false
SHOW_SEARCH_LINK=true
SHOW_THINKING_PROCESS=true
BASE_URL=https://generativelanguage.googleapis.com/v1beta
MAX_FAILURES=10
MAX_RETRIES=3
CHECK_INTERVAL_HOURS=1
TIMEZONE=America/Sao_Paulo
# Request timeout (seconds)
TIME_OUT=300
# Proxy server configuration (supports http and socks5)
# Example: PROXIES=["*********************:port", "socks5://host:port"]
PROXIES=[]
######################### Image Generation Related Configuration ###########################
PAID_KEY=AIzaSyxxxxxxxxxxxxxxxxxxx
CREATE_IMAGE_MODEL=imagen-3.0-generate-002
UPLOAD_PROVIDER=smms
SMMS_SECRET_TOKEN=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
PICGO_API_KEY=xxxx
CLOUDFLARE_IMGBED_URL=https://xxxxxxx.pages.dev/upload
CLOUDFLARE_IMGBED_AUTH_CODE=xxxxxxxxx
##########################################################################
######################### Stream Optimizer Related Configuration ########################
STREAM_OPTIMIZER_ENABLED=false
STREAM_MIN_DELAY=0.016
STREAM_MAX_DELAY=0.024
STREAM_SHORT_TEXT_THRESHOLD=10
STREAM_LONG_TEXT_THRESHOLD=50
STREAM_CHUNK_SIZE=5
##########################################################################
######################### Log Configuration #######################################
# Log level (debug, info, warning, error, critical), default is info
LOG_LEVEL=info
# Whether to enable automatic deletion of error logs
AUTO_DELETE_ERROR_LOGS_ENABLED=true
# Automatically delete error logs older than how many days (1, 7, 30)
AUTO_DELETE_ERROR_LOGS_DAYS=7
# Whether to enable automatic deletion of request logs
AUTO_DELETE_REQUEST_LOGS_ENABLED=false
# Automatically delete request logs older than how many days (1, 7, 30)
AUTO_DELETE_REQUEST_LOGS_DAYS=30
##########################################################################

# Fake Streaming Configuration
FAKE_STREAM_ENABLED=True  # Whether to enable fake streaming output
FAKE_STREAM_EMPTY_DATA_INTERVAL_SECONDS=5  # Fake streaming send empty data interval (seconds)

# Safety Settings (JSON string format)
# Note: The example values here may need to be adjusted based on actual model support
SAFETY_SETTINGS='[{"category": "HARM_CATEGORY_HARASSMENT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "OFF"}, {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_CIVIC_INTEGRITY", "threshold": "BLOCK_NONE"}]'
