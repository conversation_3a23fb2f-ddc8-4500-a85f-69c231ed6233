{% extends "base.html" %} {% block title %}API Key Status - Gemini Balance{%
endblock %} {% block head_extra_styles %}
<style>
  /* keys_status.html specific styles */
  .key-content {
    transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out,
      padding 0.3s ease-in-out;
    /* Added padding transition */
    overflow: hidden;
    /* Keep hidden initially and during collapse */
  }

  .key-content.collapsed {
    max-height: 0 !important;
    /* Use important to override inline style during transition */
    opacity: 0;
    padding-top: 0 !important;
    /* Collapse padding */
    padding-bottom: 0 !important;
    /* Collapse padding */
    /* overflow: hidden; */
    /* Already set above */
  }

  .toggle-icon {
    transition: transform 0.3s ease;
  }

  .toggle-icon.collapsed {
    transform: rotate(-90deg);
  }

  /* Copy status styling is handled by base.html's notification */

  /* Modern data statistics style */
  .stats-dashboard {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
    position: relative;
    z-index: 10;
  }

  @media (min-width: 768px) {
    .stats-dashboard {
      grid-template-columns: 1fr 1fr;
    }
  }

  /* Statistics card style */
  .stats-card {
    background-color: rgba(70, 50, 150, 0.7);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.15),
      0 2px 4px -1px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(120, 100, 200, 0.4);
    overflow: hidden;
    transition: all 0.3s ease-in-out;
  }

  .stats-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),
      0 4px 6px -2px rgba(0, 0, 0, 0.1);
    border-color: rgba(150, 130, 230, 0.6);
  }

  .stats-card-header {
    background-color: rgba(80, 60, 160, 0.8);
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(120, 100, 200, 0.4);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    /* Allow wrapping for smaller screens */
    gap: 0.5rem;
    /* Add gap between items */
  }

  .stats-card-title {
    display: flex;
    align-items: center;
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .stats-card-title i {
    margin-right: 0.5rem;
    color: #c4b5fd;
  }

  .stats-card-header h2 {
    color: #ffffff;
    /* Set list title to white */
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    padding: 0.75rem;
  }

  /* Statistics item style */
  .stat-item {
    padding: 0.75rem;
    border-radius: 0.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
  }

  .stat-item::before {
    content: "";
    position: absolute;
    inset: 0;
    opacity: 0.15;
    background-color: currentColor;
    z-index: 0;
    transition: opacity 0.3s ease-in-out;
  }

  .stat-item:hover::before {
    opacity: 0.25;
  }

  .stat-item:hover {
    transform: scale(1.05);
  }

  .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    z-index: 10;
    position: relative;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .stat-label {
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: 0.25rem;
    z-index: 10;
    position: relative;
    color: #e2e8f0;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }

  .stat-icon {
    position: absolute;
    right: 0.5rem;
    bottom: 0.25rem;
    opacity: 0.15;
    font-size: 1.875rem;
    transform: rotate(12deg);
    transition: all 0.3s ease-in-out;
  }

  .stat-item:hover .stat-icon {
    opacity: 0.25;
    transform: scale(1.1) rotate(0deg);
  }

  /* Statistics type style */
  .stat-primary {
    color: #c4b5fd;
    background-color: rgba(107, 70, 193, 0.4);
  }

  .stat-success {
    color: #6ee7b7;
    background-color: rgba(16, 150, 100, 0.35);
  }

  .stat-danger {
    color: #fca5b3;
    background-color: rgba(225, 50, 100, 0.35);
  }

  .stat-warning {
    color: #fde68a;
    background-color: rgba(200, 160, 20, 0.35);
  }

  .stat-info {
    color: #bfdbfe;
    background-color: rgba(80, 130, 220, 0.35);
  }

  /* New: Adjust hover background color for API call statistics items, make it darker and more theme-integrated */
  .stat-item.stat-warning:hover {
    background-color: rgba(200,
        160,
        20,
        0.55) !important;
    /* Adjusted from hover:bg-amber-100 */
  }

  .stat-item.stat-info:hover {
    background-color: rgba(80,
        130,
        220,
        0.55) !important;
    /* Adjusted from hover:bg-blue-100 */
  }

  .stat-item.stat-primary:hover {
    background-color: rgba(107,
        70,
        193,
        0.6) !important;
    /* Adjusted from hover:bg-indigo-100 */
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .stats-dashboard {
      gap: 1rem;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 0.5rem;
      padding: 0.5rem;
    }

    .stat-item {
      padding: 0.5rem;
    }

    .stat-value {
      font-size: 1.25rem;
    }

    .stat-label {
      font-size: 0.625rem;
    }

    .stats-card-header {
      padding: 0.5rem 0.75rem;
    }

    /* Adjust header padding */
    .key-content ul {
      grid-template-columns: 1fr;
    }

    /* Stack keys vertically on small screens */
  }

  /* Tailwind Toggle Switch Helper CSS */
  .toggle-checkbox:checked {
    @apply: right-0 border-primary-600;
    right: 0;
    border-color: #4f46e5;
  }

  .toggle-checkbox:checked+.toggle-label {
    @apply: bg-primary-600;
    background-color: #4f46e5;
  }

  /* Pagination Controls */
  #validPaginationControls,
  #invalidPaginationControls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1rem;
    /* mt-4 */
    gap: 0.5rem;
    /* space-x-2 */
  }

  /* Ensure list items are flex for alignment */
  #validKeys li,
  #invalidKeys li {
    display: flex;
    align-items: flex-start;
    /* Align checkbox with top of content */
    gap: 0.75rem;
    /* gap-3 */
  }

  /* Ensure grid layout for key lists */
  #validKeys,
  #invalidKeys {
    display: grid;
    grid-template-columns: 1fr;
    /* Default single column */
    gap: 0.75rem;
    /* gap-3 */
  }

  @media (min-width: 768px) {

    /* md breakpoint */
    #validKeys,
    #invalidKeys {
      grid-template-columns: repeat(2,
          1fr);
      /* Two columns on medium screens and up */
    }
  }

  /* Modify key list background and card style */
  .key-content {
    background-color: rgba(70, 50, 150, 0.6) !important;
  }

  #validKeys li,
  #invalidKeys li {
    background-color: rgba(80, 60, 160, 0.85);
    border: 1px solid rgba(120, 100, 200, 0.4);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    padding-left: 2.5rem;
    /* Leave space for custom checkbox */
  }

  #validKeys li:hover,
  #invalidKeys li:hover {
    border-color: rgba(150, 130, 230, 0.7);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    background-color: rgba(90, 70, 170, 0.9);
  }

  #validKeys li.selected,
  #invalidKeys li.selected {
    background-color: rgba(100, 80, 180, 0.95);
    border-color: rgba(160, 140, 240, 0.8);
  }

  /* Hide native checkbox */
  .key-checkbox {
    display: none;
  }

  /* Custom checkbox style */
  #validKeys li::before,
  #invalidKeys li::before {
    content: "";
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.25rem;
    /* 20px */
    height: 1.25rem;
    /* 20px */
    border: 2px solid rgba(180, 160, 250, 0.7);
    border-radius: 0.375rem;
    /* 6px */
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease-in-out;
  }

  #validKeys li.selected::before,
  #invalidKeys li.selected::before {
    background-color: #a78bfa;
    /* Purple background */
    border-color: #8b5cf6;
    /* Dark purple border */
  }

  /* Custom checkbox checkmark style */
  #validKeys li.selected::after,
  #invalidKeys li.selected::after {
    content: "\f00c";
    /* Font Awesome check icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    left: calc(0.75rem + 0.3rem);
    /* Adjust checkmark position */
    top: 50%;
    transform: translateY(-50%) scale(0.9);
    color: white;
    font-size: 0.8rem;
  }

  .key-text {
    color: #e9d5ff !important;
    /* Modify API key color to light purple and ensure priority */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    font-weight: 500;
  }

  /* Modal background color adjustment */
  #apiCallDetailsModal .bg-white,
  #keyUsageDetailsModal .bg-white,
  #resultModal .bg-white,
  #resetModal .bg-white,
  #verifyModal .bg-white {
    background-color: rgba(70, 50, 150, 0.95);
    color: #ffffff;
    border-color: rgba(120, 100, 200, 0.4);
  }

  /* Modal title color */
  #apiCallDetailsModalTitle,
  #keyUsageDetailsModalTitle,
  #resultModalTitle,
  #resetModalTitle,
  #verifyModalTitle {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    font-weight: 600;
  }

  /* Key usage details modal header specific style */
  #keyUsageDetailsModal .bg-white>div.border-b {
    /* For the bottom border of the header area */
    border-bottom-color: rgba(120, 100, 200, 0.6);
  }

  /* Modal message text color */
  #apiCallDetailsContent,
  #keyUsageDetailsContent,
  #resultModalMessage,
  #resetModalMessage,
  #verifyModalMessage {
    color: #f8fafc;
  }

  /* Specific adjustment for message area style in operation result modal */
  #resultModalMessage {
    background-color: rgba(80, 60, 160, 0.7) !important;
    /* Dark background */
    border-color: rgba(120, 100, 200, 0.5) !important;
    /* Coordinated border */
    color: #f8fafc !important;
    /* Ensure light text */
  }

  /* Dark theme style for normal information list in batch verification results (e.g., successful key list) */
  #resultModalMessage ul[class*="bg-gray-50"] {
    /* For ul with specific gray background color */
    background-color: rgba(80,
        60,
        160,
        0.3) !important;
    /* Dark, transparent purple background */
    border-color: rgba(120, 100, 200, 0.4) !important;
    /* Coordinated border */
  }

  #resultModalMessage ul[class*="bg-gray-50"] li {
    color: #e2e8f0 !important;
    /* Light text */
  }

  /* Dark theme style for failure list in batch verification results */
  #resultModalMessage ul[class*="bg-red-50"] {
    /* For ul with specific background color */
    background-color: rgba(127,
        29,
        29,
        0.3) !important;
    /* Dark, transparent red background */
    border-color: rgba(153, 27, 27, 0.5) !important;
    /* Dark red border */
  }

  /* Key text in failure list (e.g., AIza...lJ6E) */
  #resultModalMessage ul[class*="bg-red-50"] li span.font-mono {
    color: #fecaca !important;
    /* Light red text */
  }

  /* "Collapse/Expand" button in failure list */
  #resultModalMessage ul[class*="bg-red-50"] li button[class*="bg-red-200"] {
    background-color: rgba(185, 28, 28, 0.4) !important;
    /* Dark red button background */
    color: #fee2e2 !important;
    /* Light red button text */
    border: 1px solid rgba(220, 38, 38, 0.6) !important;
    /* Button border */
    box-shadow: none !important;
  }

  #resultModalMessage ul[class*="bg-red-50"] li button[class*="bg-red-200"]:hover {
    background-color: rgba(200, 38, 38, 0.55) !important;
    /* Button background on hover */
    color: #fef2f2 !important;
    /* Button text on hover */
  }

  /* Error details box in failure list */
  #resultModalMessage ul[class*="bg-red-50"] li div[id^="error-details-"] {
    background-color: rgba(153, 27, 27, 0.35) !important;
    /* Dark error details background */
    border-color: rgba(185, 28, 28, 0.5) !important;
    /* Error details border */
    color: #fca5a5 !important;
    /* Light red error details text */
  }

  /* Key usage details modal table header style */
  #keyUsageDetailsModal #keyUsageDetailsContent table th {
    background-color: rgba(80, 60, 160, 0.8);
    /* Consistent with statistics card header background color */
    border-bottom: 1px solid rgba(120, 100, 200, 0.4);
    /* Consistent with statistics card header border color */
    color: #e2e8f0 !important;
    /* Ensure text color is light */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    /* Maintain text shadow consistency */
  }

  #keyUsageDetailsModal #keyUsageDetailsContent table td {
    color: #f8fafc !important;
    /* Set light text, !important ensures override of Tailwind classes */
    /* If needed, add text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1); to add subtle depth */
  }

  /* Button text color */
  .stats-card button {
    color: #ffffff;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }

  /* Form control background */
  .form-input,
  .form-select {
    background-color: rgba(80, 60, 160, 0.8);
    color: #ffffff;
    border-color: rgba(120, 100, 200, 0.5);
  }

  /* Label text color */
  .text-gray-500,
  .text-gray-600,
  .text-gray-700 {
    color: #e2e8f0 !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }

  /* Adjust global background color to be more coordinated with purple background */
  .glass-card {
    background-color: rgba(80, 60, 160, 0.3) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(150, 130, 230, 0.3);
  }

  /* Pagination control style enhancement */
  .pagination-button {
    background-color: rgba(80, 60, 160, 0.8);
    color: #ffffff;
    border: 1px solid rgba(120, 100, 200, 0.4);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }

  .pagination-button.active {
    background-color: rgba(120, 100, 200, 0.9);
    border-color: rgba(150, 130, 230, 0.7);
  }

  /* Status tag enhancement */
  .inline-flex.items-center.px-2\.5.py-0\.5.rounded-full {
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }

  /* Button color enhancement */
  button.bg-success-600,
  button.bg-blue-600,
  button.bg-slate-500,
  button.bg-purple-600,
  button.bg-primary-700,
  button.bg-teal-600 {
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }

  /* Checkbox text style */
  input[type="checkbox"]+label {
    color: #f1f5f9 !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }

  /* Footer copyright text color correction */
  .fixed.bottom-0.left-0.w-full.py-3.bg-white .text-gray-200,
  .fixed.bottom-0.left-0.w-full.py-3.bg-white .text-gray-300,
  .fixed.bottom-0.left-0.w-full.py-3.bg-white .text-gray-400,
  .fixed.bottom-0.left-0.w-full.py-3.bg-white .text-gray-500,
  .fixed.bottom-0.left-0.w-full.py-3.bg-white .text-gray-600,
  .fixed.bottom-0.left-0.w-full.py-3.bg-white .text-gray-700 {
    color: #1f2937 !important;
    /* Use darker gray for better contrast */
    text-shadow: none !important;
  }

  /* Navigation link hover style (copied from config_editor.html) */
  .nav-link {
    transition: all 0.2s ease-in-out;
  }

  .nav-link:hover {
    background-color: rgba(120, 100, 200, 0.6) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  /* New: Key list internal button background and hover color adjustment */
  /* Verify button (green) */
  #validKeys li button.bg-success-600,
  #invalidKeys li button.bg-success-600 {
    background-color: rgba(22, 163, 74, 0.65) !important;
    border: 1px solid rgba(22, 163, 74, 0.85);
  }

  #validKeys li button.bg-success-600:hover,
  #invalidKeys li button.bg-success-600:hover {
    background-color: rgba(21, 128, 61, 0.75) !important;
    border-color: rgba(21, 128, 61, 0.95);
  }

  /* Reset button (blue) */
  #validKeys li button.bg-blue-600,
  #invalidKeys li button.bg-blue-600 {
    background-color: rgba(37, 99, 235, 0.65) !important;
    border: 1px solid rgba(37, 99, 235, 0.85);
  }

  #validKeys li button.bg-blue-600:hover,
  #invalidKeys li button.bg-blue-600:hover {
    background-color: rgba(29, 78, 216, 0.75) !important;
    border-color: rgba(29, 78, 216, 0.95);
  }

  /* Copy button (gray) */
  #validKeys li button.bg-slate-500,
  #invalidKeys li button.bg-slate-500 {
    background-color: rgba(100, 116, 139, 0.65) !important;
    border: 1px solid rgba(100, 116, 139, 0.85);
  }

  #validKeys li button.bg-slate-500:hover,
  #invalidKeys li button.bg-slate-500:hover {
    background-color: rgba(71, 85, 105, 0.75) !important;
    border-color: rgba(71, 85, 105, 0.95);
  }

  /* Details button (purple) */
  #validKeys li button.bg-purple-600,
  #invalidKeys li button.bg-purple-600 {
    background-color: rgba(147,
        51,
        234,
        0.6) !important;
    /* Tailwind purple-600 is 9333ea. Using purple-500 (a855f7) as base for rgba */
    border: 1px solid rgba(147, 51, 234, 0.8);
  }

  #validKeys li button.bg-purple-600:hover,
  #invalidKeys li button.bg-purple-600:hover {
    background-color: rgba(126,
        34,
        206,
        0.7) !important;
    /* Tailwind purple-600 as base for hover */
    border-color: rgba(126, 34, 206, 0.9);
  }

  /* Delete button (red) - HTML uses bg-red-800 */
  #validKeys li button.bg-red-800,
  #invalidKeys li button.bg-red-800 {
    background-color: rgba(185, 28, 28, 0.65) !important;
    /* Based on red-700 */
    border: 1px solid rgba(185, 28, 28, 0.85);
  }

  #validKeys li button.bg-red-800:hover,
  #invalidKeys li button.bg-red-800:hover {
    background-color: rgba(153, 27, 27, 0.75) !important;
    /* Based on red-800 */
    border-color: rgba(153, 27, 27, 0.95);
  }

  /* New: Key list internal status tag color adjustment */
  /* Valid tag (green) */
  #validKeys li span.bg-success-50.text-success-600,
  #invalidKeys li span.bg-success-50.text-success-600 {
    background-color: rgba(16, 185, 129, 0.28) !important;
    color: #86efac !important;
    /* Slightly lighter green for text */
    border: 1px solid rgba(16, 185, 129, 0.45);
  }

  /* Failure count tag (yellow) */
  #validKeys li span.bg-amber-50.text-amber-600,
  #invalidKeys li span.bg-amber-50.text-amber-600 {
    background-color: rgba(245, 158, 11, 0.28) !important;
    color: #fde047 !important;
    /* Slightly lighter amber for text */
    border: 1px solid rgba(245, 158, 11, 0.45);
    position: absolute;
    /* Move to bottom right */
    bottom: 0.75rem;
    /* Match li's p-3 padding */
    right: 0.75rem;
    z-index: 5;
    /* Ensure on top of other elements */
  }

  /* Invalid tag (red) - for invalidKeys list */
  #invalidKeys li span.bg-danger-50.text-danger-600 {
    background-color: rgba(239, 68, 68, 0.28) !important;
    color: #fca5a5 !important;
    /* Standard danger text color often works well */
    border: 1px solid rgba(239, 68, 68, 0.45);
  }

  /* Remove border from the last row's cells in API Call Details Modal table */
  #apiCallDetailsContent table tr:last-child td {
    border-bottom: none !important;
  }

  /* Restore success/failure status colors and icon colors within the API call details table */
  #apiCallDetailsContent table td span[class*="text-success"],
  #apiCallDetailsContent table td span[class*="success"] {
    color: #6ee7b7 !important;
    /* Theme success green */
  }

  #apiCallDetailsContent table td span[class*="text-success"] i,
  #apiCallDetailsContent table td span[class*="success"] i {
    color: #6ee7b7 !important;
  }

  #apiCallDetailsContent table td span[class*="text-danger"],
  #apiCallDetailsContent table td span[class*="failure"],
  #apiCallDetailsContent table td span[class*="danger"] {
    color: #fca5b3 !important;
    /* Theme failure red */
  }

  #apiCallDetailsContent table td span[class*="text-danger"] i,
  #apiCallDetailsContent table td span[class*="failure"] i,
  #apiCallDetailsContent table td span[class*="danger"] i {
    color: #fca5b3 !important;
  }

  /* End of API Call Details Modal Specific Styling Adjustments */
</style>
{% endblock %} {% block head_extra_scripts %}
<!-- keys_status.js needs to be loaded in head because it might be used by inline scripts -->
<script src="/static/js/keys_status.js"></script>
{% endblock %} {% block content %}
<div class="container max-w-6xl mx-auto px-4">
  <!-- Increased max-width -->
  <div class="glass-card rounded-2xl shadow-xl p-6 md:p-8">
    <div class="absolute top-6 right-6 flex items-center gap-3">
      <!-- Auto Refresh Switch -->
      <div class="flex items-center text-sm text-gray-600 select-none">
        <span class="mr-2">Auto Refresh</span>
        <div class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
          <input type="checkbox" name="autoRefreshToggle" id="autoRefreshToggle"
            class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
          <label for="autoRefreshToggle"
            class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
        </div>
      </div>
      <!-- Manual Refresh Button -->
      <button
        class="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full w-8 h-8 flex items-center justify-center text-primary-600 transition-all duration-300"
        onclick="refreshPage(this)" title="Manual Refresh">
        <i class="fas fa-sync-alt"></i>
      </button>
    </div>

    <h1
      class="text-3xl font-extrabold text-center text-transparent bg-clip-text bg-gradient-to-r from-violet-400 to-pink-400 mb-4">
      <img src="/static/icons/logo.png" alt="Gemini Balance Logo" class="h-9 inline-block align-middle mr-2" />
      Gemini Balance - Dashboard
    </h1>

    <!-- Navigation Tabs -->
    <div class="flex justify-center mb-8 overflow-x-auto pb-2 gap-2">
      <a href="/config"
        class="nav-link whitespace-nowrap flex items-center justify-center gap-2 px-6 py-3 font-medium rounded-lg text-gray-200 hover:text-white transition-all duration-200"
        style="background-color: rgba(107, 70, 193, 0.4)">
        <i class="fas fa-cog"></i> Config Editor
      </a>
      <a href="/keys"
        class="whitespace-nowrap flex items-center justify-center gap-2 px-6 py-3 font-medium rounded-lg bg-violet-600 text-white shadow-md">
        <i class="fas fa-tachometer-alt"></i> Dashboard
      </a>
      <a href="/logs"
        class="nav-link whitespace-nowrap flex items-center justify-center gap-2 px-6 py-3 font-medium rounded-lg text-gray-200 hover:text-white transition-all duration-200"
        style="background-color: rgba(107, 70, 193, 0.4)">
        <i class="fas fa-exclamation-triangle"></i> Error Logs
      </a>
    </div>

    <!-- Modern statistics panel -->
    <div class="stats-dashboard animate-fade-in" style="animation-delay: 0.1s">
      <!-- Key statistics card -->
      <div class="stats-card">
        <div class="stats-card-header">
          <h3 class="stats-card-title">
            <i class="fas fa-key"></i>
            <span>Key Stats</span>
          </h3>
          <span class="text-xs text-gray-500">Total: {{ total_keys }}</span>
        </div>
        <div class="stats-grid">
          <div class="stat-item stat-primary" title="Total Keys">
            <div class="stat-value">{{ total_keys }}</div>
            <div class="stat-label">Total Keys</div>
            <i class="stat-icon fas fa-key"></i>
          </div>
          <div class="stat-item stat-success" title="Valid Keys">
            <div class="stat-value">{{ valid_key_count }}</div>
            <div class="stat-label">Valid Keys</div>
            <i class="stat-icon fas fa-check-circle"></i>
          </div>
          <div class="stat-item stat-danger" title="Invalid Keys">
            <div class="stat-value">{{ invalid_key_count }}</div>
            <div class="stat-label">Invalid Keys</div>
            <i class="stat-icon fas fa-times-circle"></i>
          </div>
        </div>
      </div>

      <!-- API Call Statistics Card -->
      <div class="stats-card">
        <div class="stats-card-header">
          <h3 class="stats-card-title">
            <i class="fas fa-chart-line"></i>
            <span>API Call Stats</span>
          </h3>
          <span class="text-xs text-gray-500">This month: {{ api_stats.calls_month.total }}</span>
        </div>
        <div class="stats-grid">
          <div class="stat-item stat-warning cursor-pointer hover:bg-amber-100"
            title="Click to view 1 minute call details" data-period="1m"
            onclick="showApiCallDetails('1m', '{{ api_stats.calls_1m.total }}', '{{ api_stats.calls_1m.success }}', '{{ api_stats.calls_1m.failure }}')">
            <div class="stat-value">{{ api_stats.calls_1m.total }}</div>
            <div class="stat-label">1 min calls</div>
            <i class="stat-icon fas fa-stopwatch"></i>
          </div>
          <div class="stat-item stat-info cursor-pointer hover:bg-blue-100" title="Click to view 1 hour call details"
            data-period="1h"
            onclick="showApiCallDetails('1h', '{{ api_stats.calls_1h.total }}', '{{ api_stats.calls_1h.success }}', '{{ api_stats.calls_1h.failure }}')">
            <div class="stat-value">{{ api_stats.calls_1h.total }}</div>
            <div class="stat-label">1 hour calls</div>
            <i class="stat-icon fas fa-hourglass-half"></i>
          </div>
          <div class="stat-item stat-primary cursor-pointer hover:bg-indigo-100"
            title="Click to view 24 hour call details" data-period="24h"
            onclick="showApiCallDetails('24h', '{{ api_stats.calls_24h.total }}', '{{ api_stats.calls_24h.success }}', '{{ api_stats.calls_24h.failure }}')">
            <div class="stat-value">{{ api_stats.calls_24h.total }}</div>
            <div class="stat-label">24 hour calls</div>
            <i class="stat-icon fas fa-calendar-day"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Valid Key Area -->
    <div class="stats-card mb-6 animate-fade-in" style="animation-delay: 0.2s">
      <div class="stats-card-header cursor-pointer" onclick="toggleSection(this, 'validKeys')">
        <!-- Left side: Title and Toggle Icon -->
        <div class="flex items-center gap-3 flex-shrink-0">
          <!-- Prevent shrinking -->
          <i class="fas fa-chevron-down toggle-icon text-primary-600"></i>
          <i class="fas fa-check-circle text-success-500"></i>
          <h2 class="text-lg font-semibold whitespace-nowrap">
            Valid Key List ({{ valid_key_count }})
          </h2>
        </div>
        <!-- Middle: Filters and Search (Allow wrapping) -->
        <div class="flex items-center gap-x-4 gap-y-2 flex-grow flex-wrap justify-start md:justify-center">
          <!-- Allow wrapping, center on medium+ -->
          <!-- Failure count filter -->
          <div class="flex items-center gap-1">
            <label for="failCountThreshold" class="text-sm text-gray-600 select-none whitespace-nowrap">Failure Count
              ≥</label>
            <input type="number" id="failCountThreshold" value="0" min="0"
              class="form-input h-7 w-16 px-2 py-1 text-sm border rounded focus:ring-primary-500 focus:border-primary-500"
              onclick="event.stopPropagation();" />
          </div>
          <!-- Key search -->
          <div class="flex items-center gap-1">
            <label for="keySearchInput" class="text-sm text-gray-600 select-none whitespace-nowrap"><i
                class="fas fa-search mr-1"></i>Search</label>
            <input type="search" id="keySearchInput" placeholder="Enter key..."
              class="form-input h-7 w-32 px-2 py-1 text-sm border rounded focus:ring-primary-500 focus:border-primary-500"
              onclick="event.stopPropagation();" />
          </div>
          <!-- Items per page -->
          <div class="flex items-center gap-1">
            <label for="itemsPerPageSelect" class="text-sm text-gray-600 select-none whitespace-nowrap">Items per
              page</label>
            <select id="itemsPerPageSelect"
              class="form-select h-7 px-2 py-1 text-sm border rounded focus:ring-primary-500 focus:border-primary-500"
              onclick="event.stopPropagation();">
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span class="text-sm text-gray-600 select-none">items</span>
          </div>
        </div>
        <!-- Right side: Select All -->
        <div class="flex items-center gap-1 flex-shrink-0" onclick="event.stopPropagation();">
          <!-- Prevent shrinking -->
          <input type="checkbox" id="selectAllValid"
            class="form-checkbox h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
            onchange="toggleSelectAll('valid', this.checked)" />
          <label for="selectAllValid" class="text-sm text-gray-600 select-none whitespace-nowrap">Select All</label>
        </div>
      </div>

      <!-- Batch operation button group (only visible when selected) -->
      <div id="validBatchActions" class="p-3 border-t hidden flex items-center flex-wrap gap-3" style="
          background-color: rgba(80, 60, 160, 0.8);
          border-color: rgba(120, 100, 200, 0.4);
        ">
        <!-- Added flex-wrap -->
        <span class="text-sm font-medium text-gray-200 whitespace-nowrap">Selected <span
            id="validSelectedCount">0</span> items</span>
        <button
          class="flex items-center gap-2 bg-teal-600 hover:bg-teal-700 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          onclick="event.stopPropagation(); showVerifyModal('valid', event)" disabled>
          <i class="fas fa-check-double"></i> Batch Verify
        </button>
        <button
          class="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          onclick="event.stopPropagation(); resetAllKeysFailCount('valid', event)" data-reset-type="valid" disabled>
          <i class="fas fa-redo-alt"></i> Batch Reset
        </button>
        <button
          class="flex items-center gap-2 bg-primary-700 hover:bg-primary-800 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          onclick="event.stopPropagation(); copySelectedKeys('valid')" disabled>
          <i class="fas fa-copy"></i> Batch Copy
        </button>
        <button
          class="flex items-center gap-2 bg-red-800 hover:bg-red-900 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          onclick="event.stopPropagation(); showDeleteConfirmationModal('valid', event)" disabled>
          <i class="fas fa-trash-alt"></i> Batch Delete
        </button>
      </div>

      <div class="key-content p-4 bg-white bg-opacity-40">
        <!-- Key list will be populated by JS -->
        <ul id="validKeys" class="grid grid-cols-1 md:grid-cols-2 gap-3">
          {# Initial keys rendered by server-side for non-JS users or initial
          load #} {# JS will replace this content with paginated/filtered
          results #} {% if valid_keys %} {% for key, fail_count in
          valid_keys.items() %}
          <li
            class="bg-white rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-success-300 transform hover:-translate-y-1"
            data-fail-count="{{ fail_count }}" data-key="{{ key }}">
            <!-- Checkbox -->
            <input type="checkbox"
              class="form-checkbox h-5 w-5 text-primary-600 border-gray-300 rounded focus:ring-primary-500 mt-1 key-checkbox"
              data-key-type="valid" value="{{ key }}" />
            <!-- Key Info -->
            <div class="flex-grow">
              <div class="flex flex-col justify-between h-full gap-3">
                <div class="flex flex-wrap items-center gap-2">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-50 text-success-600">
                    <i class="fas fa-check mr-1"></i> Valid
                  </span>
                  <div class="flex items-center gap-1">
                    <span class="key-text font-mono" data-full-key="{{ key }}">{{ key[:4] + '...' + key[-4:] }}</span>
                    <button class="text-gray-500 hover:text-primary-600 transition-colors"
                      onclick="toggleKeyVisibility(this)" title="Show/Hide Key">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-50 text-amber-600">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    Failures: {{ fail_count }}
                  </span>
                </div>
                <div class="flex flex-wrap items-center gap-2">
                  <button
                    class="flex items-center gap-1 bg-success-600 hover:bg-success-700 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    onclick="verifyKey('{{ key }}', this)">
                    <i class="fas fa-check-circle"></i>
                    Verify
                  </button>
                  <button
                    class="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    onclick="resetKeyFailCount('{{ key }}', this)">
                    <i class="fas fa-redo-alt"></i>
                    Reset
                  </button>
                  <button
                    class="flex items-center gap-1 bg-slate-500 hover:bg-slate-600 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    onclick="copyKey('{{ key }}')">
                    <i class="fas fa-copy"></i>
                    Copy
                  </button>
                  <button
                    class="flex items-center gap-1 bg-purple-600 hover:bg-purple-700 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    onclick="showKeyUsageDetails('{{ key }}')">
                    <i class="fas fa-chart-pie"></i>
                    Details
                  </button>
                  <button
                    class="flex items-center gap-1 bg-red-800 hover:bg-red-900 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    onclick="showSingleKeyDeleteConfirmModal('{{ key }}', this)">
                    <i class="fas fa-trash-alt"></i>
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </li>
          {% endfor %} {% else %}
          <li class="text-center text-gray-500 py-4 col-span-full">
            No valid keys found
          </li>
          {% endif %}
        </ul>
        <!-- Valid Key Pagination Controls Container -->
        <div id="validPaginationControls" class="flex justify-center items-center mt-4 space-x-2">
          <!-- Pagination controls will be generated by JS -->
        </div>
      </div>
    </div>

    <!-- Invalid Key Area -->
    <div class="stats-card mb-6 animate-fade-in" style="animation-delay: 0.4s">
      <div class="stats-card-header cursor-pointer" onclick="toggleSection(this, 'invalidKeys')">
        <!-- Left side: Title and Toggle Icon -->
        <div class="flex items-center gap-3 flex-shrink-0">
          <!-- Prevent shrinking -->
          <i class="fas fa-chevron-down toggle-icon text-primary-600"></i>
          <i class="fas fa-times-circle text-danger-500"></i>
          <h2 class="text-lg font-semibold whitespace-nowrap">
            Invalid Key List ({{ invalid_key_count }})
          </h2>
        </div>
        <!-- Right side: Select All -->
        <div class="flex items-center gap-1 ml-auto flex-shrink-0" onclick="event.stopPropagation();">
          <!-- Use ml-auto, Prevent shrinking -->
          <input type="checkbox" id="selectAllInvalid"
            class="form-checkbox h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
            onchange="toggleSelectAll('invalid', this.checked)" />
          <label for="selectAllInvalid" class="text-sm text-gray-600 select-none whitespace-nowrap">Select All</label>
        </div>
      </div>

      <!-- Batch operation button group (only visible when selected) -->
      <div id="invalidBatchActions" class="p-3 border-t hidden flex items-center flex-wrap gap-3" style="
          background-color: rgba(80, 60, 160, 0.8);
          border-color: rgba(120, 100, 200, 0.4);
        ">
        <!-- Added flex-wrap -->
        <span class="text-sm font-medium text-gray-200 whitespace-nowrap">Selected <span
            id="invalidSelectedCount">0</span> items</span>
        <button
          class="flex items-center gap-2 bg-teal-600 hover:bg-teal-700 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          onclick="event.stopPropagation(); showVerifyModal('invalid', event)" disabled>
          <i class="fas fa-check-double"></i> Batch Verify
        </button>
        <button
          class="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          onclick="event.stopPropagation(); resetAllKeysFailCount('invalid', event)" data-reset-type="invalid" disabled>
          <i class="fas fa-redo-alt"></i> Batch Reset
        </button>
        <button
          class="flex items-center gap-2 bg-primary-700 hover:bg-primary-800 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          onclick="event.stopPropagation(); copySelectedKeys('invalid')" disabled>
          <i class="fas fa-copy"></i> Batch Copy
        </button>
        <button
          class="flex items-center gap-2 bg-red-800 hover:bg-red-900 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          onclick="event.stopPropagation(); showDeleteConfirmationModal('invalid', event)" disabled>
          <i class="fas fa-trash-alt"></i> Batch Delete
        </button>
      </div>

      <div class="key-content p-4 bg-white bg-opacity-40">
        <!-- Key list will be populated by JS -->
        <ul id="invalidKeys" class="grid grid-cols-1 md:grid-cols-2 gap-3">
          {# Initial keys rendered by server-side #} {# JS will replace this
          content with paginated results #} {% if invalid_keys %} {% for key,
          fail_count in invalid_keys.items() %}
          <li
            class="bg-white rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-danger-300 transform hover:-translate-y-1"
            data-key="{{ key }}">
            <!-- Checkbox -->
            <input type="checkbox"
              class="form-checkbox h-5 w-5 text-primary-600 border-gray-300 rounded focus:ring-primary-500 mt-1 key-checkbox"
              data-key-type="invalid" value="{{ key }}" />
            <!-- Key Info -->
            <div class="flex-grow">
              <div class="flex flex-col justify-between h-full gap-3">
                <div class="flex flex-wrap items-center gap-2">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-danger-50 text-danger-600">
                    <i class="fas fa-times mr-1"></i> Invalid
                  </span>
                  <div class="flex items-center gap-1">
                    <span class="key-text font-mono" data-full-key="{{ key }}">{{ key[:4] + '...' + key[-4:] }}</span>
                    <button class="text-gray-500 hover:text-primary-600 transition-colors"
                      onclick="toggleKeyVisibility(this)" title="Show/Hide Key">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-50 text-amber-600">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    Failures: {{ fail_count }}
                  </span>
                </div>
                <div class="flex flex-wrap items-center gap-2">
                  <button
                    class="flex items-center gap-1 bg-success-600 hover:bg-success-700 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    onclick="verifyKey('{{ key }}', this)">
                    <i class="fas fa-check-circle"></i>
                    Verify
                  </button>
                  <button
                    class="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    onclick="resetKeyFailCount('{{ key }}', this)">
                    <i class="fas fa-redo-alt"></i>
                    Reset
                  </button>
                  <button
                    class="flex items-center gap-1 bg-slate-500 hover:bg-slate-600 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    onclick="copyKey('{{ key }}')">
                    <i class="fas fa-copy"></i>
                    Copy
                  </button>
                  <button
                    class="flex items-center gap-1 bg-purple-600 hover:bg-purple-700 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    onclick="showKeyUsageDetails('{{ key }}')">
                    <i class="fas fa-chart-pie"></i>
                    Details
                  </button>
                  <button
                    class="flex items-center gap-1 bg-red-800 hover:bg-red-900 text-white px-2.5 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    onclick="showSingleKeyDeleteConfirmModal('{{ key }}', this)">
                    <i class="fas fa-trash-alt"></i>
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </li>
          {% endfor %} {% else %}
          <li class="text-center text-gray-500 py-4 col-span-full">
            No invalid keys found
          </li>
          {% endif %}
        </ul>
        <!-- Invalid Key Pagination Controls Container -->
        <div id="invalidPaginationControls" class="flex justify-center items-center mt-4 space-x-2">
          <!-- Pagination controls will be generated by JS -->
        </div>
      </div>
    </div>

    <!-- Removed old total keys display -->
  </div>
</div>

<!-- Scroll buttons are now in base.html -->
<div class="scroll-buttons">
  <button class="scroll-button" onclick="scrollToTop()" title="Back to top">
    <i class="fas fa-chevron-up"></i>
  </button>
  <button class="scroll-button" onclick="scrollToBottom()" title="Scroll to bottom">
    <i class="fas fa-chevron-down"></i>
  </button>
</div>

<!-- Notification component is now in base.html (use id="notification") -->
<div id="notification" class="notification"></div>
<!-- Reset Confirmation Modal -->
<div id="resetModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg p-6 shadow-xl max-w-md w-full animate-fade-in">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-800" id="resetModalTitle">
        Batch Reset Failure Count
      </h3>
      <button onclick="closeResetModal()" class="text-gray-500 hover:text-gray-700 focus:outline-none">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="mb-6">
      <p class="text-gray-600" id="resetModalMessage"></p>
    </div>
    <div class="flex justify-end gap-3">
      <button onclick="closeResetModal()"
        class="px-3 py-1.5 text-xs font-medium bg-slate-500 hover:bg-slate-600 text-white rounded-lg transition-colors">
        Cancel
      </button>
      <button id="confirmResetBtn"
        class="px-3 py-1.5 text-xs font-medium bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
        Confirm
      </button>
    </div>
  </div>
</div>
<!-- Verify Confirmation Modal -->
<div id="verifyModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg p-6 shadow-xl max-w-md w-full animate-fade-in" style="
      background-color: rgba(70, 50, 150, 0.95);
      color: #ffffff;
      border-color: rgba(120, 100, 200, 0.4);
    ">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold" id="verifyModalTitle" style="
          color: #ffffff;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          font-weight: 600;
        ">
        Batch Verify Keys
      </h3>
      <button onclick="closeVerifyModal()" class="text-gray-300 hover:text-white focus:outline-none">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="mb-6">
      <p style="color: #f8fafc" id="verifyModalMessage"></p>
    </div>
    <div class="flex justify-end gap-3">
      <button onclick="closeVerifyModal()"
        class="px-3 py-1.5 text-xs font-medium bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
        Cancel
      </button>
      <button id="confirmVerifyBtn"
        class="px-3 py-1.5 text-xs font-medium bg-teal-700 hover:bg-teal-800 text-white rounded-lg transition-colors">
        Confirm Verify
      </button>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg p-6 shadow-xl max-w-md w-full animate-fade-in" style="
      background-color: rgba(70, 50, 150, 0.95);
      color: #ffffff;
      border-color: rgba(120, 100, 200, 0.4);
    ">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold" id="deleteConfirmModalTitle" style="
          color: #ffffff;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          font-weight: 600;
        ">
        Confirm Delete
      </h3>
      <button onclick="closeDeleteConfirmationModal()" class="text-gray-300 hover:text-white focus:outline-none">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="mb-6">
      <p style="color: #f8fafc" id="deleteConfirmModalMessage"></p>
    </div>
    <div class="flex justify-end gap-3">
      <button onclick="closeDeleteConfirmationModal()"
        class="px-3 py-1.5 text-xs font-medium bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
        Cancel
      </button>
      <button id="confirmDeleteBtn"
        class="px-3 py-1.5 text-xs font-medium bg-red-700 hover:bg-red-800 text-white rounded-lg transition-colors">
        Confirm Delete
      </button>
    </div>
  </div>
</div>

<!-- New: Single Key Delete Confirmation Modal -->
<div id="singleKeyDeleteConfirmModal"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="rounded-lg p-6 shadow-xl max-w-md w-full animate-fade-in" style="
      background-color: rgba(70, 50, 150, 0.95);
      color: #ffffff;
      border-color: rgba(120, 100, 200, 0.4);
    ">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold" id="singleKeyDeleteConfirmModalTitle" style="
          color: #ffffff;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          font-weight: 600;
        ">
        Confirm Delete Key
      </h3>
      <button onclick="closeSingleKeyDeleteConfirmModal()" class="text-gray-300 hover:text-white focus:outline-none">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="mb-6">
      <p style="color: #f8fafc" id="singleKeyDeleteConfirmModalMessage"></p>
    </div>
    <div class="flex justify-end gap-3">
      <button onclick="closeSingleKeyDeleteConfirmModal()"
        class="px-3 py-1.5 text-xs font-medium bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
        Cancel
      </button>
      <button id="confirmSingleKeyDeleteBtn"
        class="px-3 py-1.5 text-xs font-medium bg-red-700 hover:bg-red-800 text-white rounded-lg transition-colors">
        Confirm Delete
      </button>
    </div>
  </div>
</div>

<!-- Operation Result Modal -->
<div id="resultModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-2xl p-0 shadow-2xl max-w-lg w-full animate-fade-in border border-gray-200">
    <div class="flex items-center justify-between px-6 pt-6 pb-2 border-b">
      <h3 class="text-xl font-bold text-gray-800 text-center w-full" id="resultModalTitle"
        style="letter-spacing: 0.05em">
        Operation Result
      </h3>
      <button onclick="closeResultModal()"
        class="absolute right-6 top-6 text-gray-400 hover:text-gray-700 focus:outline-none text-2xl">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="flex flex-col items-center px-8 pt-6 pb-2">
      <div id="resultIcon" class="text-6xl mb-3"></div>
    </div>
    <div class="px-8 pb-2 w-full">
      <div id="resultModalMessage"
        class="text-gray-700 text-base leading-relaxed break-words whitespace-pre-line max-h-80 overflow-y-auto border border-gray-100 rounded-lg bg-gray-50 p-4 shadow-inner"
        style="
          font-family: 'JetBrains Mono', 'Fira Mono', 'Consolas', 'monospace';
        ">
        <!-- Content is dynamically generated by JS -->
      </div>
    </div>
    <div class="flex justify-center px-8 pb-6 pt-2">
      <button id="resultModalConfirmBtn" onclick="closeResultModal()"
        class="px-5 py-1.5 text-sm font-semibold bg-primary-700 hover:bg-primary-800 text-white rounded-lg shadow transition-colors">
        Confirm
      </button>
    </div>
  </div>
</div>

<!-- API Call Details Modal -->
<div id="apiCallDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg p-6 shadow-xl max-w-3xl w-full animate-fade-in">
    <!-- Increased max-width -->
    <div class="flex items-center justify-between mb-4 border-b pb-3">
      <h3 class="text-xl font-semibold text-gray-800" id="apiCallDetailsModalTitle">
        API Call Details
      </h3>
      <button onclick="closeApiCallDetailsModal()" class="text-gray-500 hover:text-gray-700 focus:outline-none text-xl">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div id="apiCallDetailsContent" class="mb-6 max-h-[60vh] overflow-y-auto pr-2">
      <!-- Increased max-height and added padding-right -->
      <!-- Detailed data will be loaded here -->
      <div class="text-center py-10">
        <i class="fas fa-spinner fa-spin text-primary-600 text-3xl"></i>
        <p class="text-gray-500 mt-2">Loading...</p>
      </div>
    </div>
    <div class="flex justify-end pt-4 border-t">
      <button onclick="closeApiCallDetailsModal()"
        class="px-4 py-1.5 text-xs font-medium bg-slate-500 hover:bg-slate-600 text-white rounded-lg transition-colors">
        Close
      </button>
    </div>
  </div>
</div>

<!-- Key Usage Details Modal -->
<div id="keyUsageDetailsModal"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg p-6 shadow-xl max-w-lg w-full animate-fade-in">
    <!-- Adjusted max-width -->
    <div class="flex items-center justify-between mb-4 border-b pb-3">
      <h3 class="text-xl font-semibold text-gray-800" id="keyUsageDetailsModalTitle">
        Key Request Details
      </h3>
      <button onclick="closeKeyUsageDetailsModal()"
        class="text-gray-500 hover:text-gray-700 focus:outline-none text-xl">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div id="keyUsageDetailsContent" class="mb-6 max-h-[50vh] overflow-y-auto pr-2">
      <!-- Adjusted max-height -->
      <!-- Detailed data will be loaded here -->
      <div class="text-center py-10">
        <i class="fas fa-spinner fa-spin text-primary-600 text-3xl"></i>
        <p class="text-gray-500 mt-2">Loading...</p>
      </div>
    </div>
    <div class="flex justify-end pt-4 border-t">
      <button onclick="closeKeyUsageDetailsModal()"
        class="px-4 py-1.5 text-xs font-medium bg-slate-500 hover:bg-slate-600 text-white rounded-lg transition-colors">
        Close
      </button>
    </div>
  </div>
</div>

<!-- Footer is now in base.html -->

{% endblock %} {% block body_scripts %}
<script>
  // keys_status.html specific JavaScript initialization is now handled by keys_status.js
  // The DOMContentLoaded listener in keys_status.js will execute after the DOM is ready.
  // No inline script needed here anymore.
</script>
{% endblock %}