{% extends "base.html" %} {% block title %}Configuration Editor - <PERSON> Balance{%
endblock %} {% block head_extra_styles %}
<style>
  /* config_editor.html specific styles */
  /* Animations (already in base.html, but keep fade-in class usage) */
  .fade-in {
    animation: fadeIn 0.3s ease forwards;
  }
  /* Modal specific styles (already in base.html) */
  .array-container {
    max-height: 300px;
    overflow-y: auto;
    padding: 1rem; /* p-4 consistency */
    margin-bottom: 0.5rem; /* mb-2 consistency */
    background-color: rgba(255, 255, 255, 0.05); /* theming */
    border: 1px solid rgba(120, 100, 200, 0.3); /* theming */
    color: #e2e8f0; /* theming */
    border-radius: 0.5rem; /* rounded-lg consistency */
  }
  #API_KEYS_container {
    /* Keep specific ID styling if needed */
    max-height: 300px;
    overflow-y: auto;
  }
  .config-section {
    display: none;
    /* theming: new background and border for sections */
    background-color: rgba(70, 50, 150, 0.5);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(120, 100, 200, 0.2);
    border-radius: 0.75rem; /* rounded-xl */
    padding: 1.5rem; /* p-6 */
    margin-bottom: 1.5rem; /* mb-6 */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-lg */
  }
  .config-section.active {
    display: block;
    animation: fadeIn 0.3s ease forwards; /* Use base animation */
  }
  .provider-config {
    display: none;
  }
  .provider-config.active {
    display: block;
  }
  /* Tailwind Toggle Switch Helper CSS */
  .toggle-checkbox:checked {
    @apply: right-0 border-violet-600; /* theming: changed from primary-600 */
    right: 0;
    border-color: #7c3aed; /* theming: violet-600 */
  }
  .toggle-checkbox:checked + .toggle-label {
    @apply: bg-violet-600; /* theming: changed from primary-600 */
    background-color: #7c3aed; /* theming: violet-600 */
  }
  .toggle-label {
    /* theming: style for unchecked state */
    background-color: rgba(120, 100, 200, 0.3);
  }
  /* Unify notification style to black translucent, ensure consistency with keys_status */
  .notification {
    background: rgba(0, 0, 0, 0.8) !important;
    color: #fff !important;
  }

  /* Theming for input fields */
  .form-input-themed {
    background-color: rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(120, 100, 200, 0.5) !important;
    color: #e2e8f0 !important;
  }
  .form-input-themed::placeholder {
    color: #a0aec0 !important; /* gray-400 */
  }
  .form-input-themed:focus {
    border-color: #a78bfa !important; /* violet-400 */
    box-shadow: 0 0 0 3px rgba(167, 139, 250, 0.4) !important; /* ring-violet-400 */
  }

  /* Theming for select fields - Improved dropdown style */
  .form-select-themed {
    background-color: rgba(
      60,
      40,
      130,
      0.6
    ) !important; /* Deeper purple background, increased transparency */
    border: 1px solid rgba(167, 139, 250, 0.7) !important; /* Brighter purple border */
    color: #ffffff !important; /* Brighter text color */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23d8b4fe' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 8l4 4 4-4'/%3e%3c/svg%3e") !important; /* Brighter purple arrow */
    appearance: none !important;
    padding: 0.6rem 2.5rem 0.6rem 0.8rem !important; /* Adjust padding */
    background-repeat: no-repeat !important;
    background-position: right 0.6rem center !important;
    background-size: 1.5em 1.5em !important;
    border-radius: 0.5rem !important; /* Rounded corners */
    font-weight: 500 !important; /* Semi-bold */
    height: auto !important; /* Auto height */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important; /* Subtle shadow */
    cursor: pointer !important;
  }

  .form-select-themed:focus {
    border-color: #d8b4fe !important; /* violet-300 */
    box-shadow: 0 0 0 3px rgba(216, 180, 254, 0.4) !important; /* ring-violet-300 */
    outline: none !important;
  }

  .form-select-themed option {
    background-color: rgba(76, 29, 149, 0.95) !important; /* Dark purple background */
    color: #ffffff !important;
    padding: 8px !important;
  }

  /* Styles for different log levels */
  #LOG_LEVEL {
    font-weight: 600 !important;
  }

  #LOG_LEVEL option[value="DEBUG"] {
    background-color: rgba(79, 70, 229, 0.8) !important; /* indigo */
    color: white !important;
  }

  #LOG_LEVEL option[value="INFO"] {
    background-color: rgba(59, 130, 246, 0.8) !important; /* blue */
    color: white !important;
  }

  #LOG_LEVEL option[value="WARNING"] {
    background-color: rgba(245, 158, 11, 0.8) !important; /* amber */
    color: white !important;
  }

  #LOG_LEVEL option[value="ERROR"] {
    background-color: rgba(239, 68, 68, 0.8) !important; /* red */
    color: white !important;
  }

  #LOG_LEVEL option[value="CRITICAL"] {
    background-color: rgba(185, 28, 28, 0.8) !important; /* red-700 */
    color: white !important;
  }

  /* Thinking model budget mapping style */
  .map-item {
    background-color: rgba(60, 40, 130, 0.2) !important;
    border-radius: 0.5rem !important;
    padding: 0.5rem !important;
    border: 1px solid rgba(167, 139, 250, 0.3) !important;
    transition: all 0.2s ease-in-out !important;
  }

  .map-item:hover {
    background-color: rgba(60, 40, 130, 0.3) !important;
    border-color: rgba(167, 139, 250, 0.5) !important;
  }

  .map-key-input {
    background-color: rgba(80, 60, 170, 0.4) !important;
    border: 1px solid rgba(167, 139, 250, 0.6) !important;
    color: #e2e8f0 !important;
    font-weight: 500 !important;
    border-radius: 0.375rem !important;
  }

  .map-value-input {
    background-color: rgba(60, 40, 130, 0.4) !important;
    border: 1px solid rgba(167, 139, 250, 0.6) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    border-radius: 0.375rem !important;
    box-shadow: none !important;
    transition: all 0.2s !important;
  }

  .map-value-input:focus {
    background-color: rgba(80, 60, 170, 0.6) !important;
    border-color: #d8b4fe !important; /* violet-300 */
    box-shadow: 0 0 0 3px rgba(216, 180, 254, 0.4) !important;
    outline: none !important;
  }

  /* Warning text style */
  .warning-text {
    color: #f87171 !important; /* red-400 */
    font-weight: 600 !important;
    background-color: rgba(248, 113, 113, 0.15) !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 0.375rem !important;
    border-left: 3px solid #ef4444 !important; /* red-500 */
    display: flex !important;
    align-items: center !important;
    margin-top: 0.5rem !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  }

  .warning-text i {
    margin-right: 0.5rem !important;
    color: #ef4444 !important; /* red-500 */
  }

  /* Token generation button style */
  .generate-btn {
    background-color: rgba(80, 60, 170, 0.3) !important;
    color: #d8b4fe !important; /* violet-300 */
    border: 1px solid rgba(167, 139, 250, 0.4) !important;
    border-left: none !important;
    transition: all 0.2s ease !important;
  }

  .generate-btn:hover {
    background-color: rgba(
      109,
      40,
      217,
      0.4
    ) !important; /* violet-700 translucent */
    color: #ffffff !important;
    box-shadow: 0 0 8px rgba(167, 139, 250, 0.5) !important;
  }

  .generate-btn:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(167, 139, 250, 0.5) !important;
  }

  /* Navigation link hover style */
  .nav-link {
    transition: all 0.2s ease-in-out;
  }

  .nav-link:hover {
    background-color: rgba(120, 100, 200, 0.6) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  /* General label and small text theming */
  label {
    color: #e2e8f0 !important; /* Light gray/white for labels */
    font-weight: 500; /* semibold */
  }
  small {
    color: #cbd5e0 !important; /* Lighter gray for small text */
  }
</style>
{% endblock %} {% block content %}
<div class="container max-w-4xl mx-auto px-4">
  <div
    class="rounded-2xl shadow-xl p-6 md:p-8"
    style="
      background-color: rgba(80, 60, 160, 0.3);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(150, 130, 230, 0.3);
    "
  >
    <button
      class="absolute top-6 right-6 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full w-8 h-8 flex items-center justify-center text-violet-300 hover:text-violet-100 transition-all duration-300"
      onclick="refreshPage(this)"
    >
      <i class="fas fa-sync-alt"></i>
    </button>

    <h1
      class="text-3xl font-extrabold text-center text-transparent bg-clip-text bg-gradient-to-r from-violet-400 to-pink-400 mb-4"
    >
      <img
        src="/static/icons/logo.png"
        alt="Gemini Balance Logo"
        class="h-9 inline-block align-middle mr-2"
      />
      Gemini Balance - Configuration Editor
    </h1>

    <!-- Navigation Tabs -->
    <div class="flex justify-center mb-8 overflow-x-auto pb-2 gap-2">
      <a
        href="/config"
        class="whitespace-nowrap flex items-center justify-center gap-2 px-6 py-3 font-medium rounded-lg bg-violet-600 text-white shadow-md"
      >
        <i class="fas fa-cog"></i> Configuration Editor
      </a>
      <a
        href="/keys"
        class="nav-link whitespace-nowrap flex items-center justify-center gap-2 px-6 py-3 font-medium rounded-lg text-gray-200 hover:text-white transition-all duration-200"
        style="background-color: rgba(107, 70, 193, 0.4)"
      >
        <i class="fas fa-tachometer-alt"></i> Dashboard
      </a>
      <a
        href="/logs"
        class="nav-link whitespace-nowrap flex items-center justify-center gap-2 px-6 py-3 font-medium rounded-lg text-gray-200 hover:text-white transition-all duration-200"
        style="background-color: rgba(107, 70, 193, 0.4)"
      >
        <i class="fas fa-exclamation-triangle"></i> Error Logs
      </a>
    </div>

    <!-- Config Tabs -->
    <div class="flex justify-center mb-6 flex-wrap gap-2">
      <button
        class="tab-btn bg-violet-600 text-white px-5 py-2 rounded-full shadow-md font-medium text-sm"
        data-tab="api"
      >
        API Configuration
      </button>
      <button
        class="tab-btn bg-white bg-opacity-20 text-gray-200 px-5 py-2 rounded-full font-medium text-sm hover:bg-opacity-30 transition-all duration-200"
        data-tab="model"
      >
        Model Configuration
      </button>
      <button
        class="tab-btn bg-white bg-opacity-20 text-gray-200 px-5 py-2 rounded-full font-medium text-sm hover:bg-opacity-30 transition-all duration-200"
        data-tab="image"
      >
        Image Generation
      </button>
      <button
        class="tab-btn bg-white bg-opacity-20 text-gray-200 px-5 py-2 rounded-full font-medium text-sm hover:bg-opacity-30 transition-all duration-200"
        data-tab="stream"
      >
        Streaming Output
      </button>
      <button
        class="tab-btn bg-white bg-opacity-20 text-gray-200 px-5 py-2 rounded-full font-medium text-sm hover:bg-opacity-30 transition-all duration-200"
        data-tab="scheduler"
      >
        Scheduled Tasks
      </button>
      <button
        class="tab-btn bg-white bg-opacity-20 text-gray-200 px-5 py-2 rounded-full font-medium text-sm hover:bg-opacity-30 transition-all duration-200"
        data-tab="logging"
      >
        Logging Configuration
      </button>
    </div>

    <!-- Save Status Banner (Removed - using notification component now) -->

    <!-- Configuration Form -->
    <form id="configForm" class="mt-6">
      <!-- API Related Configuration -->
      <div class="config-section active" id="api-section">
        <h2
          class="text-xl font-bold mb-6 pb-3 border-b flex items-center gap-2 text-gray-100 border-violet-300 border-opacity-30"
        >
          <i class="fas fa-key text-violet-400"></i> API Related Configuration
        </h2>

        <!-- API Key List -->
        <div class="mb-6">
          <label for="API_KEYS" class="block font-semibold mb-2 text-gray-700"
            >API Key List</label
          >
          <div class="mb-2">
            <input
              type="search"
              id="apiKeySearchInput"
              placeholder="Search keys..."
              class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
            />
          </div>
          <div class="array-container" id="API_KEYS_container">
            <!-- Array items will be dynamically added here -->
          </div>
          <div class="flex justify-end gap-2">
            <button
              type="button"
              class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              id="bulkDeleteApiKeyBtn"
            >
              <i class="fas fa-trash-alt"></i> Delete Key
            </button>
            <button
              type="button"
              class="bg-violet-600 hover:bg-violet-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              id="addApiKeyBtn"
            >
              <i class="fas fa-plus"></i> Add Key
            </button>
          </div>
          <small class="text-gray-500 mt-1 block"
            >Gemini API key list, one per line</small
          >
        </div>

        <!-- Allowed Tokens List -->
        <div class="mb-6">
          <label
            for="ALLOWED_TOKENS"
            class="block font-semibold mb-2 text-gray-700"
            >Allowed Tokens List</label
          >
          <div class="array-container" id="ALLOWED_TOKENS_container">
            <!-- Array items will be dynamically added here -->
          </div>
          <div class="flex justify-end">
            <button
              type="button"
              class="bg-violet-600 hover:bg-violet-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              onclick="addArrayItem('ALLOWED_TOKENS')"
            >
              <i class="fas fa-plus"></i> Add Token
            </button>
          </div>
          <small class="text-gray-500 mt-1 block">List of tokens allowed to access the API</small>
        </div>

        <!-- Authentication Token -->
        <div class="mb-6">
          <label for="AUTH_TOKEN" class="block font-semibold mb-2 text-gray-700"
            >Authentication Token</label
          >
          <div class="flex items-center">
            <div
              class="flex items-center flex-grow border rounded-md focus-within:border-violet-400 focus-within:ring focus-within:ring-violet-400 focus-within:ring-opacity-50"
              style="border-color: rgba(120, 100, 200, 0.5)"
            >
              <input
                type="text"
                id="AUTH_TOKEN"
                name="AUTH_TOKEN"
                placeholder="Defaults to the first in ALLOWED_TOKENS"
                class="array-input flex-grow px-3 py-2 border-none rounded-l-md focus:outline-none sensitive-input form-input-themed"
              />
              <button
                type="button"
                id="generateAuthTokenBtn"
                class="generate-btn px-2 py-2 text-gray-400 hover:text-violet-400 focus:outline-none rounded-r-md hover:bg-gray-600 transition-colors"
                title="Generate Random Token"
                style="background-color: rgba(120, 100, 200, 0.2)"
              >
                <i class="fas fa-dice"></i>
              </button>
            </div>
          </div>
          <small class="text-gray-500 mt-1 block">Token used for API authentication</small>
        </div>

        <!-- API Base URL -->
        <div class="mb-6">
          <label for="BASE_URL" class="block font-semibold mb-2 text-gray-700"
            >API Base URL</label
          >
          <input
            type="text"
            id="BASE_URL"
            name="BASE_URL"
            placeholder="https://generativelanguage.googleapis.com/v1beta"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">Base URL for Gemini API</small>
        </div>
 
        <!-- Vertex API Key List -->
        <div class="mb-6">
          <label for="VERTEX_API_KEYS" class="block font-semibold mb-2 text-gray-700"
            >Vertex API Key List</label
          >
          <div class="array-container" id="VERTEX_API_KEYS_container">
            <!-- Array items will be dynamically added here -->
          </div>
          <div class="flex justify-end gap-2">
            <button
              type="button"
              class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              id="bulkDeleteVertexApiKeyBtn"
            >
              <i class="fas fa-trash-alt"></i> Delete Vertex Key
            </button>
            <button
              type="button"
              class="bg-violet-600 hover:bg-violet-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              id="addVertexApiKeyBtn"
            >
              <i class="fas fa-plus"></i> Add Vertex Key
            </button>
          </div>
          <small class="text-gray-500 mt-1 block"
            >Vertex AI Platform API key list. Click buttons to batch add or delete.</small
          >
        </div>
 
        <!-- Vertex Express API Base URL -->
        <div class="mb-6">
          <label for="VERTEX_EXPRESS_BASE_URL" class="block font-semibold mb-2 text-gray-700"
            >Vertex Express API Base URL</label
          >
          <input
            type="text"
            id="VERTEX_EXPRESS_BASE_URL"
            name="VERTEX_EXPRESS_BASE_URL"
            placeholder="https://aiplatform.googleapis.com/v1beta1/publishers/google/models"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">Base URL for Vertex Express API</small>
        </div>
 
        <!-- Max Failures -->
        <div class="mb-6">
          <label
            for="MAX_FAILURES"
            class="block font-semibold mb-2 text-gray-700"
            >Max Failures</label
          >
          <input
            type="number"
            id="MAX_FAILURES"
            name="MAX_FAILURES"
            min="1"
            max="100"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block"
            >Number of times an API key can fail before being marked as invalid</small
          >
        </div>

        <!-- Request Timeout -->
        <div class="mb-6">
          <label for="TIME_OUT" class="block font-semibold mb-2 text-gray-700"
            >Request Timeout (seconds)</label
          >
          <input
            type="number"
            id="TIME_OUT"
            name="TIME_OUT"
            min="1"
            max="600"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">Timeout for API requests</small>
        </div>

        <!-- Max Retries -->
        <div class="mb-6">
          <label
            for="MAX_RETRIES"
            class="block font-semibold mb-2 text-gray-700"
            >Max Retries</label
          >
          <input
            type="number"
            id="MAX_RETRIES"
            name="MAX_RETRIES"
            min="0"
            max="10"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block"
            >Maximum number of retries after an API request fails</small
          >
        </div>
        <!-- Proxy Server List -->
        <div class="mb-6">
          <label for="PROXIES" class="block font-semibold mb-2 text-gray-700"
            >Proxy Server List</label
          >
          <div class="array-container" id="PROXIES_container">
            <!-- Proxy items will be dynamically added here -->
          </div>
          <div class="flex justify-end gap-2">
            <button
              type="button"
              class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              id="bulkDeleteProxyBtn"
            >
              <i class="fas fa-trash-alt"></i> Delete Proxy
            </button>
            <button
              type="button"
              class="bg-violet-600 hover:bg-violet-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              id="addProxyBtn"
            >
              <i class="fas fa-plus"></i> Add Proxy
            </button>
          </div>
          <small class="text-gray-500 mt-1 block"
            >Proxy server list, supports http and socks5 formats, e.g.
            *********************:port or
            socks5://host:port. Click buttons to batch add or delete.</small
          >
        </div>
      </div>

      <!-- Model Related Configuration -->
      <div class="config-section" id="model-section">
        <h2
          class="text-xl font-bold mb-6 pb-3 border-b flex items-center gap-2 text-gray-100 border-violet-300 border-opacity-30"
        >
          <i class="fas fa-robot text-violet-400"></i> Model Related Configuration
        </h2>

        <!-- Test Model -->
        <div class="mb-6">
          <label for="TEST_MODEL" class="block font-semibold mb-2 text-gray-700"
            >Test Model</label
          >
          <div class="flex items-center gap-2">
            <input
              type="text"
              id="TEST_MODEL"
              name="TEST_MODEL"
              placeholder="gemini-1.5-flash"
              class="flex-grow px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
            />
            <button
              type="button"
              title="Select Model"
              class="model-helper-trigger-btn p-2 rounded-md text-violet-300 hover:bg-violet-700 transition-colors"
              data-target-input-id="TEST_MODEL"
            >
              <i class="fas fa-list-ul"></i>
            </button>
          </div>
          <small class="text-gray-500 mt-1 block">Model used for testing API keys</small>
        </div>

        <!-- Image Model List -->
        <div class="mb-6">
          <label
            for="IMAGE_MODELS"
            class="block font-semibold mb-2 text-gray-700"
            >Image Model List</label
          >
          <div class="array-container" id="IMAGE_MODELS_container">
            <!-- Array items will be dynamically added here -->
          </div>
          <div class="flex justify-end gap-2 mt-2">
            <button
              type="button"
              title="Select model from list to add below"
              class="model-helper-trigger-btn p-2 rounded-md text-violet-300 hover:bg-violet-700 transition-colors"
              data-target-array-key="IMAGE_MODELS"
            >
              <i class="fas fa-list-ul"></i>
            </button>
            <button
              type="button"
              class="bg-violet-600 hover:bg-violet-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              onclick="addArrayItem('IMAGE_MODELS')"
            >
              <i class="fas fa-plus"></i> Add Model
            </button>
          </div>
          <small class="text-gray-500 mt-1 block">List of models supporting image processing</small>
        </div>

        <!-- Search Model List -->
        <div class="mb-6">
          <label
            for="SEARCH_MODELS"
            class="block font-semibold mb-2 text-gray-700"
            >Search Model List</label
          >
          <div class="array-container" id="SEARCH_MODELS_container">
            <!-- Array items will be dynamically added here -->
          </div>
          <div class="flex justify-end gap-2 mt-2">
            <button
              type="button"
              title="Select model from list to add below"
              class="model-helper-trigger-btn p-2 rounded-md text-violet-300 hover:bg-violet-700 transition-colors"
              data-target-array-key="SEARCH_MODELS"
            >
              <i class="fas fa-list-ul"></i>
            </button>
            <button
              type="button"
              class="bg-violet-600 hover:bg-violet-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              onclick="addArrayItem('SEARCH_MODELS')"
            >
              <i class="fas fa-plus"></i> Add Model
            </button>
          </div>
          <small class="text-gray-500 mt-1 block">List of models supporting search functionality</small>
        </div>

        <!-- Filtered Model List -->
        <div class="mb-6">
          <label
            for="FILTERED_MODELS"
            class="block font-semibold mb-2 text-gray-700"
            >Filtered Model List</label
          >
          <div class="array-container" id="FILTERED_MODELS_container">
            <!-- Array items will be dynamically added here -->
          </div>
          <div class="flex justify-end gap-2 mt-2">
            <button
              type="button"
              title="Select model from list to add below"
              class="model-helper-trigger-btn p-2 rounded-md text-violet-300 hover:bg-violet-700 transition-colors"
              data-target-array-key="FILTERED_MODELS"
            >
              <i class="fas fa-list-ul"></i>
            </button>
            <button
              type="button"
              class="bg-violet-600 hover:bg-violet-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              onclick="addArrayItem('FILTERED_MODELS')"
            >
              <i class="fas fa-plus"></i> Add Model
            </button>
          </div>
          <small class="text-gray-500 mt-1 block">List of models to be filtered</small>
        </div>

        <!-- Enable Code Execution Tool -->
        <div class="mb-6 flex items-center justify-between">
          <label
            for="TOOLS_CODE_EXECUTION_ENABLED"
            class="font-semibold text-gray-700"
            >Enable Code Execution Tool</label
          >
          <div
            class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in"
          >
            <input
              type="checkbox"
              name="TOOLS_CODE_EXECUTION_ENABLED"
              id="TOOLS_CODE_EXECUTION_ENABLED"
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
            />
            <label
              for="TOOLS_CODE_EXECUTION_ENABLED"
              class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"
            ></label>
          </div>
        </div>

        <!-- Show Search Link -->
        <div class="mb-6 flex items-center justify-between">
          <label for="SHOW_SEARCH_LINK" class="font-semibold text-gray-700"
            >Show Search Link</label
          >
          <div
            class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in"
          >
            <input
              type="checkbox"
              name="SHOW_SEARCH_LINK"
              id="SHOW_SEARCH_LINK"
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
            />
            <label
              for="SHOW_SEARCH_LINK"
              class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"
            ></label>
          </div>
        </div>

        <!-- Show Thinking Process -->
        <div class="mb-6 flex items-center justify-between">
          <label for="SHOW_THINKING_PROCESS" class="font-semibold text-gray-700"
            >Show Thinking Process</label
          >
          <div
            class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in"
          >
            <input
              type="checkbox"
              name="SHOW_THINKING_PROCESS"
              id="SHOW_THINKING_PROCESS"
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
            />
            <label
              for="SHOW_THINKING_PROCESS"
              class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"
            ></label>
          </div>
        </div>

        <!-- Thinking Models List -->
        <div class="mb-6">
          <label
            for="THINKING_MODELS"
            class="block font-semibold mb-2 text-gray-700"
            >Thinking Models List</label
          >
          <div class="array-container" id="THINKING_MODELS_container">
            <!-- Array items will be dynamically added here -->
          </div>
          <div class="flex justify-end gap-2 mt-2">
            <button
              type="button"
              title="Select model from list to add below"
              class="model-helper-trigger-btn p-2 rounded-md text-violet-300 hover:bg-violet-700 transition-colors"
              data-target-array-key="THINKING_MODELS"
            >
              <i class="fas fa-list-ul"></i>
            </button>
            <button
              type="button"
              class="bg-violet-600 hover:bg-violet-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              onclick="addArrayItem('THINKING_MODELS')"
            >
              <i class="fas fa-plus"></i> Add Model
            </button>
          </div>
          <small class="text-gray-500 mt-1 block"
            >List of models used for "Thinking Process"</small
          >
        </div>

        <!-- Thinking Model Budget Mapping -->
        <div class="mb-6">
          <label
            for="THINKING_BUDGET_MAP"
            class="block font-semibold mb-2 text-gray-700"
            >Thinking Model Budget Mapping</label
          >
          <div
            class="bg-white rounded-lg border border-gray-200 p-4 mb-2 space-y-3"
            id="THINKING_BUDGET_MAP_container"
            style="
              background-color: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(120, 100, 200, 0.3);
              color: #e2e8f0;
            "
          >
            <!-- Key-value pairs will be dynamically added here -->
            <div class="text-gray-500 text-sm italic">
              Please add thinking models above first, then configure budgets here.
            </div>
          </div>
          <!-- Removed add budget mapping button -->
          <!-- <div class="flex justify-end">
                            <button type="button" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2" id="addBudgetMapItemBtn">
                                <i class="fas fa-plus"></i> Add Budget Mapping
                            </button>
                        </div> -->
          <small class="text-gray-500 mt-1 block"
            >Set budget for each thinking model (integer, max value
            24576), this item is automatically associated with the model list above.</small
          >
        </div>
        <!-- Safety Settings -->
        <div class="mb-6">
          <label
            for="SAFETY_SETTINGS"
            class="block font-semibold mb-2 text-gray-700"
            >Safety Settings</label
          >
          <div
            class="bg-white rounded-lg border border-gray-200 p-4 mb-2 space-y-3"
            id="SAFETY_SETTINGS_container"
            style="
              background-color: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(120, 100, 200, 0.3);
              color: #e2e8f0;
            "
          >
            <!-- Safety settings items will be dynamically added here -->
            <div class="text-gray-500 text-sm italic">
              Define model safety filter thresholds.
            </div>
          </div>
          <div class="flex justify-end">
            <button
              type="button"
              class="bg-violet-600 hover:bg-violet-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
              id="addSafetySettingBtn"
            >
              <i class="fas fa-plus"></i> Add Safety Setting
            </button>
          </div>
          <small class="text-gray-500 mt-1 block"
            >Configure model safety filter levels, e.g. HARM_CATEGORY_HARASSMENT:
            BLOCK_NONE.</small
          >
          <div class="warning-text">
            <i class="fas fa-exclamation-triangle"></i>
            <span
              >Recommended to set to OFF, other values may affect output speed, do not change unless necessary.</span
            >
          </div>
        </div>
      </div>

      <!-- Image Generation Related Configuration -->
      <div class="config-section" id="image-section">
        <h2
          class="text-xl font-bold mb-6 pb-3 border-b flex items-center gap-2 text-gray-100 border-violet-300 border-opacity-30"
        >
          <i class="fas fa-image text-violet-400"></i> Image Generation Configuration
        </h2>

        <!-- Paid API Key -->
        <div class="mb-6">
          <label for="PAID_KEY" class="block font-semibold mb-2 text-gray-700"
            >Paid API Key</label
          >
          <input
            type="text"
            id="PAID_KEY"
            name="PAID_KEY"
            placeholder="AIzaSyxxxxxxxxxxxxxxxxxxx"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 sensitive-input form-input-themed"
          />
          <small class="text-gray-500 mt-1 block"
            >Paid API key for image generation</small
          >
        </div>

        <!-- Image Generation Model -->
        <div class="mb-6">
          <label
            for="CREATE_IMAGE_MODEL"
            class="block font-semibold mb-2 text-gray-700"
            >Image Generation Model</label
          >
          <div class="flex items-center gap-2">
            <input
              type="text"
              id="CREATE_IMAGE_MODEL"
              name="CREATE_IMAGE_MODEL"
              placeholder="imagen-3.0-generate-002"
              class="flex-grow px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
            />
            <button
              type="button"
              title="Select Model"
              class="model-helper-trigger-btn p-2 rounded-md text-violet-300 hover:bg-violet-700 transition-colors"
              data-target-input-id="CREATE_IMAGE_MODEL"
            >
              <i class="fas fa-list-ul"></i>
            </button>
          </div>
          <small class="text-gray-500 mt-1 block">Model used for image generation</small>
        </div>

        <!-- Upload Provider -->
        <div class="mb-6">
          <label
            for="UPLOAD_PROVIDER"
            class="block font-semibold mb-2 text-gray-700"
            >Upload Provider</label
          >
          <select
            id="UPLOAD_PROVIDER"
            name="UPLOAD_PROVIDER"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-select-themed"
          >
            <option value="smms" selected>SM.MS</option>
            <option value="picgo">PicGo</option>
            <option value="cloudflare_imgbed">Cloudflare</option>
          </select>
          <small class="text-gray-500 mt-1 block">Image upload service provider</small>
        </div>

        <!-- SM.MS Key -->
        <div class="mb-6 provider-config active" data-provider="smms">
          <label
            for="SMMS_SECRET_TOKEN"
            class="block font-semibold mb-2 text-gray-700"
            >SM.MS Key</label
          >
          <input
            type="text"
            id="SMMS_SECRET_TOKEN"
            name="SMMS_SECRET_TOKEN"
            placeholder="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 sensitive-input form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">SM.MS image bed key</small>
        </div>

        <!-- PicGo API Key -->
        <div class="mb-6 provider-config" data-provider="picgo">
          <label
            for="PICGO_API_KEY"
            class="block font-semibold mb-2 text-gray-700"
            >PicGo API Key</label
          >
          <input
            type="text"
            id="PICGO_API_KEY"
            name="PICGO_API_KEY"
            placeholder="xxxx"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 sensitive-input form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">PicGo API key</small>
        </div>

        <!-- Cloudflare Image Bed URL -->
        <div class="mb-6 provider-config" data-provider="cloudflare_imgbed">
          <label
            for="CLOUDFLARE_IMGBED_URL"
            class="block font-semibold mb-2 text-gray-700"
            >Cloudflare Image Bed URL</label
          >
          <input
            type="text"
            id="CLOUDFLARE_IMGBED_URL"
            name="CLOUDFLARE_IMGBED_URL"
            placeholder="https://xxxxxxx.pages.dev/upload"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">Cloudflare image bed URL</small>
        </div>

        <!-- Cloudflare Auth Code -->
        <div class="mb-6 provider-config" data-provider="cloudflare_imgbed">
          <label
            for="CLOUDFLARE_IMGBED_AUTH_CODE"
            class="block font-semibold mb-2 text-gray-700"
            >Cloudflare Auth Code</label
          >
          <input
            type="text"
            id="CLOUDFLARE_IMGBED_AUTH_CODE"
            name="CLOUDFLARE_IMGBED_AUTH_CODE"
            placeholder="xxxxxxxxx"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 sensitive-input form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">Cloudflare image bed authentication code</small>
        </div>
      </div>

      <!-- Streaming Output Optimizer Configuration -->
      <div class="config-section" id="stream-section">
        <h2
          class="text-xl font-bold mb-6 pb-3 border-b flex items-center gap-2 text-gray-100 border-violet-300 border-opacity-30"
        >
          <i class="fas fa-stream text-violet-400"></i> Streaming Output Optimizer
        </h2>

        <!-- Enable Streaming Output Optimization -->
        <div class="mb-6 flex items-center justify-between">
          <label
            for="STREAM_OPTIMIZER_ENABLED"
            class="font-semibold text-gray-700"
            >Enable Streaming Output Optimization</label
          >
          <div
            class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in"
          >
            <input
              type="checkbox"
              name="STREAM_OPTIMIZER_ENABLED"
              id="STREAM_OPTIMIZER_ENABLED"
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
            />
            <label
              for="STREAM_OPTIMIZER_ENABLED"
              class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"
            ></label>
          </div>
        </div>

        <!-- Minimum Delay -->
        <div class="mb-6">
          <label
            for="STREAM_MIN_DELAY"
            class="block font-semibold mb-2 text-gray-700"
            >Minimum Delay (seconds)</label
          >
          <input
            type="number"
            id="STREAM_MIN_DELAY"
            name="STREAM_MIN_DELAY"
            min="0"
            max="1"
            step="0.001"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">Minimum delay for streaming output</small>
        </div>

        <!-- Maximum Delay -->
        <div class="mb-6">
          <label
            for="STREAM_MAX_DELAY"
            class="block font-semibold mb-2 text-gray-700"
            >Maximum Delay (seconds)</label
          >
          <input
            type="number"
            id="STREAM_MAX_DELAY"
            name="STREAM_MAX_DELAY"
            min="0"
            max="1"
            step="0.001"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">Maximum delay for streaming output</small>
        </div>

        <!-- Short Text Threshold -->
        <div class="mb-6">
          <label
            for="STREAM_SHORT_TEXT_THRESHOLD"
            class="block font-semibold mb-2 text-gray-700"
            >Short Text Threshold</label
          >
          <input
            type="number"
            id="STREAM_SHORT_TEXT_THRESHOLD"
            name="STREAM_SHORT_TEXT_THRESHOLD"
            min="1"
            max="100"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">Character threshold for short text</small>
        </div>

        <!-- Long Text Threshold -->
        <div class="mb-6">
          <label
            for="STREAM_LONG_TEXT_THRESHOLD"
            class="block font-semibold mb-2 text-gray-700"
            >Long Text Threshold</label
          >
          <input
            type="number"
            id="STREAM_LONG_TEXT_THRESHOLD"
            name="STREAM_LONG_TEXT_THRESHOLD"
            min="1"
            max="1000"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">Character threshold for long text</small>
        </div>

        <!-- Chunk Size -->
        <div class="mb-6">
          <label
            for="STREAM_CHUNK_SIZE"
            class="block font-semibold mb-2 text-gray-700"
            >Chunk Size</label
          >
          <input
            type="number"
            id="STREAM_CHUNK_SIZE"
            name="STREAM_CHUNK_SIZE"
            min="1"
            max="100"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block">Chunk size for streaming output</small>
        </div>

        <!-- Fake Streaming Configuration -->
        <h3
          class="text-lg font-semibold mb-4 pt-4 border-t border-violet-300 border-opacity-20 text-gray-200"
        >
          <i class="fas fa-ghost text-violet-400"></i> Fake Streaming Configuration
        </h3>

        <!-- Enable Fake Streaming Output -->
        <div class="mb-6 flex items-center justify-between">
          <label for="FAKE_STREAM_ENABLED" class="font-semibold text-gray-700"
            >Enable Fake Streaming Output</label
          >
          <div
            class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in"
          >
            <input
              type="checkbox"
              name="FAKE_STREAM_ENABLED"
              id="FAKE_STREAM_ENABLED"
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
            />
            <label
              for="FAKE_STREAM_ENABLED"
              class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"
            ></label>
          </div>
        </div>
        <small class="text-gray-500 mt-1 block mb-4"
          >When enabled, non-streaming interface is called and empty data is sent during waiting to keep connection alive.</small
        >

        <!-- Fake Streaming Empty Data Interval (seconds) -->
        <div class="mb-6">
          <label
            for="FAKE_STREAM_EMPTY_DATA_INTERVAL_SECONDS"
            class="block font-semibold mb-2 text-gray-700"
            >Fake Streaming Empty Data Interval (seconds)</label
          >
          <input
            type="number"
            id="FAKE_STREAM_EMPTY_DATA_INTERVAL_SECONDS"
            name="FAKE_STREAM_EMPTY_DATA_INTERVAL_SECONDS"
            min="1"
            max="60"
            step="1"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block"
            >Interval for sending empty data to client to keep connection alive when fake streaming is enabled (recommended
            3-10 seconds).</small
          >
        </div>
      </div>

      <!-- Scheduled Task Configuration -->
      <div class="config-section" id="scheduler-section">
        <h2
          class="text-xl font-bold mb-6 pb-3 border-b flex items-center gap-2 text-gray-100 border-violet-300 border-opacity-30"
        >
          <i class="fas fa-clock text-violet-400"></i> Scheduled Task Configuration
        </h2>

        <!-- Check Interval (hours) -->
        <div class="mb-6">
          <label
            for="CHECK_INTERVAL_HOURS"
            class="block font-semibold mb-2 text-gray-700"
            >Check Interval (hours)</label
          >
          <input
            type="number"
            id="CHECK_INTERVAL_HOURS"
            name="CHECK_INTERVAL_HOURS"
            min="1"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block"
            >Interval for scheduled key status checks (hours)</small
          >
        </div>

        <!-- Timezone -->
        <div class="mb-6">
          <label for="TIMEZONE" class="block font-semibold mb-2 text-gray-700"
            >Timezone</label
          >
          <input
            type="text"
            id="TIMEZONE"
            name="TIMEZONE"
            placeholder="e.g. Asia/Shanghai"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-input-themed"
          />
          <small class="text-gray-500 mt-1 block"
            >Timezone used for scheduled tasks, format like "Asia/Shanghai" or "UTC"</small
          >
        </div>
      </div>

      <!-- Logging Configuration -->
      <div class="config-section" id="logging-section">
        <h2
          class="text-xl font-bold mb-6 pb-3 border-b flex items-center gap-2 text-gray-100 border-violet-300 border-opacity-30"
        >
          <i class="fas fa-file-alt text-violet-400"></i> Logging Configuration
        </h2>

        <!-- Log Level -->
        <div class="mb-6">
          <label for="LOG_LEVEL" class="block font-semibold mb-2 text-gray-700"
            >Log Level</label
          >
          <select
            id="LOG_LEVEL"
            name="LOG_LEVEL"
            class="w-full px-4 py-3 rounded-lg form-select-themed"
          >
            <option value="DEBUG">DEBUG</option>
            <option value="INFO">INFO</option>
            <option value="WARNING">WARNING</option>
            <option value="ERROR">ERROR</option>
            <option value="CRITICAL">CRITICAL</option>
          </select>
          <small class="text-gray-500 mt-1 block"
            >Set the logging verbosity for the application</small
          >
        </div>

        <!-- Enable Auto Delete Error Logs -->
        <div class="mb-6">
          <div class="flex items-center justify-between">
            <label
              for="AUTO_DELETE_ERROR_LOGS_ENABLED"
              class="font-semibold text-gray-700"
              >Enable Auto Delete Error Logs</label
            >
            <div
              class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in"
            >
              <input
                type="checkbox"
                name="AUTO_DELETE_ERROR_LOGS_ENABLED"
                id="AUTO_DELETE_ERROR_LOGS_ENABLED"
                class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
              />
              <label
                for="AUTO_DELETE_ERROR_LOGS_ENABLED"
                class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"
              ></label>
            </div>
          </div>
          <small class="text-gray-500 mt-1 block"
            >When enabled, error logs older than the specified number of days will be automatically deleted.</small
          >
        </div>

        <!-- Auto Delete Error Logs Older Than (days) -->
        <div class="mb-6">
          <label
            for="AUTO_DELETE_ERROR_LOGS_DAYS"
            class="block font-semibold mb-2 text-gray-700"
            >Auto Delete Error Logs Older Than (days)</label
          >
          <select
            id="AUTO_DELETE_ERROR_LOGS_DAYS"
            name="AUTO_DELETE_ERROR_LOGS_DAYS"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-select-themed"
          >
            <option value="1">1 Day</option>
            <option value="7">7 Days</option>
            <option value="30">30 Days</option>
          </select>
          <small class="text-gray-500 mt-1 block"
            >Select the number of days for automatic error log deletion.</small
          >
        </div>

        <!-- Enable Auto Delete Request Logs -->
        <div class="mb-6">
          <div class="flex items-center justify-between">
            <label
              for="AUTO_DELETE_REQUEST_LOGS_ENABLED"
              class="font-semibold text-gray-700"
              >Enable Auto Delete Request Logs</label
            >
            <div
              class="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in"
            >
              <input
                type="checkbox"
                name="AUTO_DELETE_REQUEST_LOGS_ENABLED"
                id="AUTO_DELETE_REQUEST_LOGS_ENABLED"
                class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
              />
              <label
                for="AUTO_DELETE_REQUEST_LOGS_ENABLED"
                class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"
              ></label>
            </div>
          </div>
          <small class="text-gray-500 mt-1 block"
            >When enabled, request logs older than the specified number of days will be automatically deleted.</small
          >
        </div>

        <!-- Auto Delete Request Logs Older Than (days) -->
        <div class="mb-6">
          <label
            for="AUTO_DELETE_REQUEST_LOGS_DAYS"
            class="block font-semibold mb-2 text-gray-700"
            >Auto Delete Request Logs Older Than (days)</label
          >
          <select
            id="AUTO_DELETE_REQUEST_LOGS_DAYS"
            name="AUTO_DELETE_REQUEST_LOGS_DAYS"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 form-select-themed"
          >
            <option value="1">1 Day</option>
            <option value="7">7 Days</option>
            <option value="30">30 Days</option>
          </select>
          <small class="text-gray-500 mt-1 block"
            >Select the number of days for automatic request log deletion.</small
          >
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col md:flex-row justify-center gap-4 mt-8">
        <button
          type="button"
          id="saveBtn"
          class="bg-gradient-to-r from-violet-500 to-pink-500 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg flex items-center justify-center gap-2"
        >
          <i class="fas fa-save"></i> Save Configuration
        </button>
        <button
          type="button"
          id="resetBtn"
          class="bg-gradient-to-r from-gray-600 to-gray-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg flex items-center justify-center gap-2"
          style="
            background-image: linear-gradient(
              to right,
              rgba(107, 70, 193, 0.7),
              rgba(120, 100, 200, 0.8)
            );
          "
        >
          <i class="fas fa-undo"></i> Reset Configuration
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Scroll buttons are now in base.html -->
<div class="scroll-buttons">
  <button class="scroll-button" onclick="scrollToTop()" title="Back to top">
    <i class="fas fa-chevron-up"></i>
  </button>
  <button class="scroll-button" onclick="scrollToBottom()" title="Scroll to bottom">
    <i class="fas fa-chevron-down"></i>
  </button>
</div>

<!-- Notification component is now in base.html -->
<div id="notification" class="notification"></div>
<!-- Footer is now in base.html -->

<!-- API Key Add Modal -->
<div id="apiKeyModal" class="modal">
  <div
    class="w-full max-w-lg mx-auto rounded-2xl shadow-2xl overflow-hidden animate-fade-in"
    style="
      background-color: rgba(70, 50, 150, 0.95);
      color: #ffffff;
      border: 1px solid rgba(120, 100, 200, 0.4);
    "
  >
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-100">Batch Add API Keys</h2>
        <button
          id="closeApiKeyModalBtn"
          class="text-gray-300 hover:text-gray-100 text-xl"
        >
          ×
        </button>
      </div>
      <p class="text-gray-300 mb-4">
        Paste one or more keys per line, valid keys will be extracted and deduplicated automatically.
      </p>
      <textarea
        id="apiKeyBulkInput"
        rows="10"
        placeholder="Paste API keys here..."
        class="w-full px-4 py-3 rounded-lg border font-mono text-sm form-input-themed"
      ></textarea>
      <div class="flex justify-end gap-3 mt-6">
        <button
          type="button"
          id="confirmAddApiKeyBtn"
          class="bg-violet-600 hover:bg-violet-700 text-white px-6 py-2 rounded-lg font-medium transition"
        >
          Confirm Add
        </button>
        <button
          type="button"
          id="cancelAddApiKeyBtn"
          class="bg-gray-500 bg-opacity-50 hover:bg-opacity-70 text-gray-200 px-6 py-2 rounded-lg font-medium transition"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Bulk Delete API Key Modal -->
<div id="bulkDeleteApiKeyModal" class="modal">
  <div
    class="w-full max-w-lg mx-auto rounded-2xl shadow-2xl overflow-hidden animate-fade-in"
    style="
      background-color: rgba(70, 50, 150, 0.95);
      color: #ffffff;
      border: 1px solid rgba(120, 100, 200, 0.4);
    "
  >
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-100">Batch Delete API Keys</h2>
        <button
          id="closeBulkDeleteModalBtn"
          class="text-gray-300 hover:text-gray-100 text-xl"
        >
          ×
        </button>
      </div>
      <p class="text-gray-300 mb-4">
        Paste one or more keys per line, valid keys will be extracted and deleted from the list automatically.
      </p>
      <textarea
        id="bulkDeleteApiKeyInput"
        rows="10"
        placeholder="Paste API keys to delete here..."
        class="w-full px-4 py-3 rounded-lg border font-mono text-sm form-input-themed focus:border-red-500 focus:ring-red-500"
      ></textarea>
      <div class="flex justify-end gap-3 mt-6">
        <button
          type="button"
          id="confirmBulkDeleteApiKeyBtn"
          class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition"
        >
          Confirm Delete
        </button>
        <button
          type="button"
          id="cancelBulkDeleteApiKeyBtn"
          class="bg-gray-500 bg-opacity-50 hover:bg-opacity-70 text-gray-200 px-6 py-2 rounded-lg font-medium transition"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Proxy Add Modal -->
<div id="proxyModal" class="modal">
  <div
    class="w-full max-w-lg mx-auto rounded-2xl shadow-2xl overflow-hidden animate-fade-in"
    style="
      background-color: rgba(70, 50, 150, 0.95);
      color: #ffffff;
      border: 1px solid rgba(120, 100, 200, 0.4);
    "
  >
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-100">Batch Add Proxy Servers</h2>
        <button
          id="closeProxyModalBtn"
          class="text-gray-300 hover:text-gray-100 text-xl"
        >
          ×
        </button>
      </div>
      <p class="text-gray-300 mb-4">
        Paste one or more proxy addresses per line, valid addresses will be extracted and deduplicated automatically.
      </p>
      <textarea
        id="proxyBulkInput"
        rows="10"
        placeholder="Paste proxy addresses here (e.g. *********************:port or socks5://host:port)..."
        class="w-full px-4 py-3 rounded-lg border font-mono text-sm form-input-themed"
      ></textarea>
      <div class="flex justify-end gap-3 mt-6">
        <button
          type="button"
          id="confirmAddProxyBtn"
          class="bg-violet-600 hover:bg-violet-700 text-white px-6 py-2 rounded-lg font-medium transition"
        >
          Confirm Add
        </button>
        <button
          type="button"
          id="cancelAddProxyBtn"
          class="bg-gray-500 bg-opacity-50 hover:bg-opacity-70 text-gray-200 px-6 py-2 rounded-lg font-medium transition"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Bulk Delete Proxy Modal -->
<div id="bulkDeleteProxyModal" class="modal">
  <div
    class="w-full max-w-lg mx-auto rounded-2xl shadow-2xl overflow-hidden animate-fade-in"
    style="
      background-color: rgba(70, 50, 150, 0.95);
      color: #ffffff;
      border: 1px solid rgba(120, 100, 200, 0.4);
    "
  >
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-100">Batch Delete Proxy Servers</h2>
        <button
          id="closeBulkDeleteProxyModalBtn"
          class="text-gray-300 hover:text-gray-100 text-xl"
        >
          ×
        </button>
      </div>
      <p class="text-gray-300 mb-4">
        Paste one or more proxy addresses per line, valid addresses will be extracted and deleted from the list automatically.
      </p>
      <textarea
        id="bulkDeleteProxyInput"
        rows="10"
        placeholder="Paste proxy addresses to delete here..."
        class="w-full px-4 py-3 rounded-lg border font-mono text-sm form-input-themed focus:border-red-500 focus:ring-red-500"
      ></textarea>
      <div class="flex justify-end gap-3 mt-6">
        <button
          type="button"
          id="confirmBulkDeleteProxyBtn"
          class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition"
        >
          Confirm Delete
        </button>
        <button
          type="button"
          id="cancelBulkDeleteProxyBtn"
          class="bg-gray-500 bg-opacity-50 hover:bg-opacity-70 text-gray-200 px-6 py-2 rounded-lg font-medium transition"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Reset Confirmation Modal -->
<div id="resetConfirmModal" class="modal">
  <div
    class="w-full max-w-md mx-auto rounded-2xl shadow-2xl overflow-hidden animate-fade-in"
    style="
      background-color: rgba(70, 50, 150, 0.95);
      color: #ffffff;
      border: 1px solid rgba(120, 100, 200, 0.4);
    "
  >
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-100">Confirm Reset Configuration</h2>
        <button
          id="closeResetModalBtn"
          class="text-gray-300 hover:text-gray-100 text-xl"
        >
          ×
        </button>
      </div>
      <p class="text-gray-300 mb-6">
        Are you sure you want to reset all configurations?<br />This will revert to default values, and this operation is irreversible.
      </p>
      <div class="flex justify-end gap-3">
        <button
          type="button"
          id="confirmResetBtn"
          class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg font-medium transition"
        >
          Confirm Reset
        </button>
        <button
          type="button"
          id="cancelResetBtn"
          class="bg-violet-600 hover:bg-violet-700 text-white px-6 py-2 rounded-lg font-medium transition"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>
 
<!-- Vertex API Key Add Modal -->
<div id="vertexApiKeyModal" class="modal">
  <div
    class="w-full max-w-lg mx-auto rounded-2xl shadow-2xl overflow-hidden animate-fade-in"
    style="
      background-color: rgba(70, 50, 150, 0.95);
      color: #ffffff;
      border: 1px solid rgba(120, 100, 200, 0.4);
    "
  >
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-100">Batch Add Vertex API Keys</h2>
        <button
          id="closeVertexApiKeyModalBtn"
          class="text-gray-300 hover:text-gray-100 text-xl"
        >
          ×
        </button>
      </div>
      <p class="text-gray-300 mb-4">
        Paste one or more keys per line, valid keys will be extracted (format: starts with AQ., 53 characters in total) and deduplicated.
      </p>
      <textarea
        id="vertexApiKeyBulkInput"
        rows="10"
        placeholder="Paste Vertex API keys here..."
        class="w-full px-4 py-3 rounded-lg border font-mono text-sm form-input-themed"
      ></textarea>
      <div class="flex justify-end gap-3 mt-6">
        <button
          type="button"
          id="confirmAddVertexApiKeyBtn"
          class="bg-violet-600 hover:bg-violet-700 text-white px-6 py-2 rounded-lg font-medium transition"
        >
          Confirm Add
        </button>
        <button
          type="button"
          id="cancelAddVertexApiKeyBtn"
          class="bg-gray-500 bg-opacity-50 hover:bg-opacity-70 text-gray-200 px-6 py-2 rounded-lg font-medium transition"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>
 
<!-- Bulk Delete Vertex API Key Modal -->
<div id="bulkDeleteVertexApiKeyModal" class="modal">
  <div
    class="w-full max-w-lg mx-auto rounded-2xl shadow-2xl overflow-hidden animate-fade-in"
    style="
      background-color: rgba(70, 50, 150, 0.95);
      color: #ffffff;
      border: 1px solid rgba(120, 100, 200, 0.4);
    "
  >
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-100">Batch Delete Vertex API Keys</h2>
        <button
          id="closeBulkDeleteVertexModalBtn"
          class="text-gray-300 hover:text-gray-100 text-xl"
        >
          ×
        </button>
      </div>
      <p class="text-gray-300 mb-4">
        Paste one or more keys per line, valid keys will be extracted and deleted from the list automatically.
      </p>
      <textarea
        id="bulkDeleteVertexApiKeyInput"
        rows="10"
        placeholder="Paste Vertex API keys to delete here..."
        class="w-full px-4 py-3 rounded-lg border font-mono text-sm form-input-themed focus:border-red-500 focus:ring-red-500"
      ></textarea>
      <div class="flex justify-end gap-3 mt-6">
        <button
          type="button"
          id="confirmBulkDeleteVertexApiKeyBtn"
          class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition"
        >
          Confirm Delete
        </button>
        <button
          type="button"
          id="cancelBulkDeleteVertexApiKeyBtn"
          class="bg-gray-500 bg-opacity-50 hover:bg-opacity-70 text-gray-200 px-6 py-2 rounded-lg font-medium transition"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>
 
<!-- Model Helper Modal -->
<div id="modelHelperModal" class="modal">
  <div
    class="w-full max-w-lg mx-auto rounded-2xl shadow-2xl overflow-hidden animate-fade-in"
    style="
      background-color: rgba(70, 50, 150, 0.95);
      color: #ffffff;
      border: 1px solid rgba(120, 100, 200, 0.4);
    "
  >
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 id="modelHelperTitle" class="text-xl font-bold text-gray-100">
          Select Model
        </h2>
        <button
          id="closeModelHelperModalBtn"
          class="text-gray-300 hover:text-gray-100 text-xl"
        >
          ×
        </button>
      </div>
      <input
        type="text"
        id="modelHelperSearchInput"
        placeholder="Search models..."
        class="w-full px-4 py-3 mb-4 rounded-lg border font-mono text-sm form-input-themed"
      />
      <div
        id="modelHelperListContainer"
        class="array-container"
        style="max-height: 300px; overflow-y: auto"
      >
        <!-- Model items will be populated here -->
        <p class="text-gray-400 text-sm italic">Loading model list...</p>
      </div>
      <div class="flex justify-end gap-3 mt-6">
        <button
          type="button"
          id="cancelModelHelperBtn"
          class="bg-gray-500 bg-opacity-50 hover:bg-opacity-70 text-gray-200 px-6 py-2 rounded-lg font-medium transition"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>

{% endblock %} {% block body_scripts %}
<script src="/static/js/config_editor.js"></script>
<!-- Enhance dropdown style and interactivity -->
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Enhance interactivity for all dropdowns
    const selects = document.querySelectorAll(".form-select-themed");

    selects.forEach((select) => {
      // Add change event to apply selected effect
      select.addEventListener("change", function () {
        this.classList.add("selected");
        // Set subtle animation effect
        this.style.transition = "all 0.2s ease";
        this.style.transform = "scale(1.02)";
        setTimeout(() => {
          this.style.transform = "scale(1)";
        }, 200);
      });

      // Add focus event
      select.addEventListener("focus", function () {
        this.style.boxShadow = "0 0 0 3px rgba(216, 180, 254, 0.5)";
      });

      // Add selected style based on whether a value is selected
      if (select.value && select.value !== "") {
        select.classList.add("selected");
      }
    });

    // Beautify log level selection display
    const logLevelSelect = document.getElementById("LOG_LEVEL");
    if (logLevelSelect) {
      // Add different styles for different log levels
      const updateLogLevelStyle = () => {
        const value = logLevelSelect.value;

        // Set different styles based on different log levels
        switch (value) {
          case "DEBUG":
            logLevelSelect.style.borderColor = "rgba(129, 140, 248, 0.8)";
            logLevelSelect.style.color = "#c7d2fe"; // indigo-200
            break;
          case "INFO":
            logLevelSelect.style.borderColor = "rgba(96, 165, 250, 0.8)";
            logLevelSelect.style.color = "#bfdbfe"; // blue-200
            break;
          case "WARNING":
            logLevelSelect.style.borderColor = "rgba(251, 191, 36, 0.8)";
            logLevelSelect.style.color = "#fde68a"; // amber-200
            break;
          case "ERROR":
            logLevelSelect.style.borderColor = "rgba(239, 68, 68, 0.8)";
            logLevelSelect.style.color = "#fecaca"; // red-200
            break;
          case "CRITICAL":
            logLevelSelect.style.borderColor = "rgba(220, 38, 38, 0.8)";
            logLevelSelect.style.color = "#fca5a5"; // red-300
            break;
          default:
            logLevelSelect.style.borderColor = "rgba(167, 139, 250, 0.7)";
            logLevelSelect.style.color = "#ffffff";
        }
      };

      // Update style on initialization and change
      updateLogLevelStyle();
      logLevelSelect.addEventListener("change", updateLogLevelStyle);
    }

    // Enhance budget mapping item interaction
    const budgetInputs = document.querySelectorAll(".map-value-input");
    budgetInputs.forEach((input) => {
      // Add focus and hover effects
      input.addEventListener("focus", function () {
        const parentItem = this.closest(".map-item");
        if (parentItem) {
          parentItem.style.backgroundColor = "rgba(60, 40, 130, 0.4)";
          parentItem.style.borderColor = "rgba(167, 139, 250, 0.7)";
        }
      });

      input.addEventListener("blur", function () {
        const parentItem = this.closest(".map-item");
        if (parentItem) {
          parentItem.style.backgroundColor = "";
          parentItem.style.borderColor = "";
        }
      });

      // Limit input to positive integers and not exceed max value
      input.addEventListener("input", function () {
        let val = this.value.replace(/[^0-9]/g, "");
        if (val !== "") {
          val = parseInt(val, 10);
          if (val > 24576) val = 24576;
        }
        this.value = val;

        // Add subtle animation feedback
        this.style.transform = "scale(1.05)";
        setTimeout(() => {
          this.style.transform = "scale(1)";
        }, 150);
      });
    });
  });
</script>
{% endblock %}